import requests

# Replace with your actual endpoint
url = "http://127.0.0.1:8000/audio/download/audio_job_abc123def456/question_0.mp3"

headers = {
    "accept": "application/json"
}

response = requests.get(url, headers=headers)

# Save the file if the request was successful
if response.status_code == 200:
    with open("downloaded_audio.mp3", "wb") as f:
        f.write(response.content)
    print("File downloaded successfully.")
else:
    print(f"Failed to download file. Status code: {response.status_code}")
