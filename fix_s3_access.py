"""
<PERSON><PERSON><PERSON> to fix S3 access issues and make existing audio files publicly accessible.
This script will:
1. Check S3 bucket access
2. List existing audio files
3. Make them publicly accessible
4. Test access to the files
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.abspath('.'))

from api.utils.s3_service import s3_service
from api.utils.tts_service import tts_service
import requests

def test_s3_bucket_access():
    """Test if we can access the S3 bucket."""
    print("🔍 Testing S3 bucket access...")
    
    if s3_service.check_bucket_access():
        print("✅ S3 bucket is accessible")
        return True
    else:
        print("❌ Cannot access S3 bucket")
        print("💡 Check your AWS credentials and bucket permissions")
        return False

def list_existing_audio_files():
    """List all existing audio files in the S3 bucket."""
    print("\n📁 Listing existing audio files...")
    
    try:
        response = s3_service.s3_client.list_objects_v2(
            Bucket=s3_service.bucket_name,
            Prefix="audio/"
        )
        
        if 'Contents' not in response:
            print("📭 No audio files found in S3 bucket")
            return []
        
        files = []
        for obj in response['Contents']:
            files.append(obj['Key'])
            print(f"  📄 {obj['Key']} ({obj['Size']} bytes)")
        
        print(f"📊 Total files found: {len(files)}")
        return files
        
    except Exception as e:
        print(f"❌ Error listing files: {e}")
        return []

def make_existing_files_public(file_keys):
    """Make existing audio files publicly accessible."""
    print(f"\n🔓 Making {len(file_keys)} files publicly accessible...")
    
    success_count = 0
    for file_key in file_keys:
        if s3_service.make_object_public(file_key):
            print(f"  ✅ {file_key}")
            success_count += 1
        else:
            print(f"  ❌ {file_key}")
    
    print(f"📊 Successfully updated {success_count}/{len(file_keys)} files")
    return success_count

def test_public_access(file_keys):
    """Test public access to the files."""
    print(f"\n🌐 Testing public access to files...")
    
    success_count = 0
    for file_key in file_keys[:3]:  # Test first 3 files only
        s3_url = f"https://{s3_service.bucket_name}.s3.{s3_service.aws_region}.amazonaws.com/{file_key}"
        
        try:
            response = requests.head(s3_url, timeout=10)
            if response.status_code == 200:
                print(f"  ✅ {file_key} - Publicly accessible")
                print(f"     🔗 {s3_url}")
                success_count += 1
            else:
                print(f"  ❌ {file_key} - Status: {response.status_code}")
        except Exception as e:
            print(f"  ❌ {file_key} - Error: {e}")
    
    print(f"📊 {success_count}/{min(3, len(file_keys))} files are publicly accessible")
    return success_count

async def test_new_audio_generation():
    """Test generating new audio with public access."""
    print("\n🎤 Testing new audio generation with public access...")
    
    try:
        # Generate a test audio file
        test_text = "This is a test audio file to verify S3 public access."
        voice_id = "21m00Tcm4TlvDq8ikWAM"  # Rachel voice
        job_id = "test_public_access"
        
        success, s3_url, duration = await tts_service.generate_audio_file(
            text=test_text,
            voice_id=voice_id,
            job_id=job_id,
            question_index=0,
            model="eleven_flash_v2_5"
        )
        
        if success:
            print(f"✅ Audio generated successfully")
            print(f"🔗 S3 URL: {s3_url}")
            
            # Test immediate access
            print("🧪 Testing immediate public access...")
            try:
                response = requests.head(s3_url, timeout=10)
                if response.status_code == 200:
                    print("✅ New file is immediately publicly accessible!")
                    print(f"📏 Content-Length: {response.headers.get('Content-Length')} bytes")
                    print(f"📄 Content-Type: {response.headers.get('Content-Type')}")
                else:
                    print(f"❌ New file not accessible: {response.status_code}")
            except Exception as e:
                print(f"❌ Error testing access: {e}")
            
            # Clean up test file
            print("🧹 Cleaning up test file...")
            s3_service.delete_audio_files(job_id)
            
        else:
            print(f"❌ Audio generation failed: {s3_url}")
            
    except Exception as e:
        print(f"❌ Error in audio generation test: {e}")

def main():
    """Main function to run all tests and fixes."""
    print("🔧 S3 Access Fix and Test Script")
    print("=" * 50)
    
    # Step 1: Test bucket access
    if not test_s3_bucket_access():
        print("\n❌ Cannot proceed without S3 bucket access")
        print("💡 Please check:")
        print("   - AWS credentials in .env file")
        print("   - S3 bucket exists and is accessible")
        print("   - AWS region is correct")
        return
    
    # Step 2: List existing files
    existing_files = list_existing_audio_files()
    
    # Step 3: Make existing files public (if any)
    if existing_files:
        make_existing_files_public(existing_files)
        
        # Step 4: Test public access
        test_public_access(existing_files)
    
    # Step 5: Test new audio generation
    print("\n" + "=" * 50)
    asyncio.run(test_new_audio_generation())
    
    print("\n🎉 S3 access fix and test completed!")
    print("\n💡 Next steps:")
    print("   1. Generate new audio using your API")
    print("   2. The S3 URLs should now be publicly accessible")
    print("   3. Test the URLs in your browser or application")

if __name__ == "__main__":
    main()
