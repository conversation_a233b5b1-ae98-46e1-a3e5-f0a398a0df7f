2025-05-27 00:25:02,646 - api_logger - INFO - Audio generation request received for 3 questions
2025-05-27 00:25:02,646 - api_logger - INFO - Created audio job: audio_job_18473864329c
2025-05-27 00:25:02,646 - api_logger - ERROR - Error creating audio generation job: 'str' object has no attribute 'value'
2025-05-27 00:32:16,100 - api_logger - INFO - Audio generation request received for 2 questions
2025-05-27 00:32:16,100 - api_logger - INFO - Created audio job: audio_job_c86a9b306ed9
2025-05-27 00:32:16,100 - api_logger - INFO - Audio generation job created: audio_job_c86a9b306ed9
2025-05-27 00:32:16,102 - api_logger - INFO - Starting background audio generation for job: audio_job_c86a9b306ed9
2025-05-27 00:32:16,102 - api_logger - INFO - Updated job audio_job_c86a9b306ed9: status=processing, progress=0
2025-05-27 00:32:16,102 - api_logger - INFO - Starting audio generation for 2 questions
2025-05-27 00:32:16,102 - api_logger - INFO - Generating audio for text: Tell me about yourself...
2025-05-27 00:32:16,104 - api_logger - ERROR - Error generating audio: 'ElevenLabs' object has no attribute 'generate'
2025-05-27 00:32:16,104 - api_logger - ERROR - Failed to generate audio for question 1: 'ElevenLabs' object has no attribute 'generate'
2025-05-27 00:32:16,104 - api_logger - INFO - Generating audio for text: What are your strengths?...
2025-05-27 00:32:16,104 - api_logger - ERROR - Error generating audio: 'ElevenLabs' object has no attribute 'generate'
2025-05-27 00:32:16,104 - api_logger - ERROR - Failed to generate audio for question 2: 'ElevenLabs' object has no attribute 'generate'
2025-05-27 00:32:16,104 - api_logger - INFO - Audio generation completed. 0/2 files generated
2025-05-27 00:32:16,104 - api_logger - INFO - Updated job audio_job_c86a9b306ed9: status=partial, progress=0
2025-05-27 00:32:16,104 - api_logger - WARNING - Partial completion for job audio_job_c86a9b306ed9: 0/2 files
2025-05-27 00:32:50,140 - api_logger - INFO - Audio generation request received for 2 questions
2025-05-27 00:32:50,140 - api_logger - INFO - Created audio job: audio_job_982a4ce84e23
2025-05-27 00:32:50,140 - api_logger - INFO - Audio generation job created: audio_job_982a4ce84e23
2025-05-27 00:32:50,142 - api_logger - INFO - Starting background audio generation for job: audio_job_982a4ce84e23
2025-05-27 00:32:50,142 - api_logger - INFO - Updated job audio_job_982a4ce84e23: status=processing, progress=0
2025-05-27 00:32:50,150 - api_logger - INFO - Starting audio generation for 2 questions
2025-05-27 00:32:50,151 - api_logger - INFO - Generating audio for text: tell me about yourself...
2025-05-27 00:32:50,151 - api_logger - ERROR - Error generating audio: 'ElevenLabs' object has no attribute 'generate'
2025-05-27 00:32:50,151 - api_logger - ERROR - Failed to generate audio for question 1: 'ElevenLabs' object has no attribute 'generate'
2025-05-27 00:32:50,151 - api_logger - INFO - Generating audio for text: what is your strength...
2025-05-27 00:32:50,151 - api_logger - ERROR - Error generating audio: 'ElevenLabs' object has no attribute 'generate'
2025-05-27 00:32:50,151 - api_logger - ERROR - Failed to generate audio for question 2: 'ElevenLabs' object has no attribute 'generate'
2025-05-27 00:32:50,151 - api_logger - INFO - Audio generation completed. 0/2 files generated
2025-05-27 00:32:50,152 - api_logger - INFO - Updated job audio_job_982a4ce84e23: status=partial, progress=0
2025-05-27 00:32:50,152 - api_logger - WARNING - Partial completion for job audio_job_982a4ce84e23: 0/2 files
2025-05-27 00:32:59,435 - api_logger - INFO - Audio generation request received for 2 questions
2025-05-27 00:32:59,435 - api_logger - INFO - Created audio job: audio_job_b5ad520f25a7
2025-05-27 00:32:59,435 - api_logger - INFO - Audio generation job created: audio_job_b5ad520f25a7
2025-05-27 00:32:59,435 - api_logger - INFO - Starting background audio generation for job: audio_job_b5ad520f25a7
2025-05-27 00:32:59,435 - api_logger - INFO - Updated job audio_job_b5ad520f25a7: status=processing, progress=0
2025-05-27 00:32:59,438 - api_logger - INFO - Starting audio generation for 2 questions
2025-05-27 00:32:59,438 - api_logger - INFO - Generating audio for text: Tell me about yourself...
2025-05-27 00:32:59,438 - api_logger - ERROR - Error generating audio: 'ElevenLabs' object has no attribute 'generate'
2025-05-27 00:32:59,438 - api_logger - ERROR - Failed to generate audio for question 1: 'ElevenLabs' object has no attribute 'generate'
2025-05-27 00:32:59,441 - api_logger - INFO - Generating audio for text: What are your strengths?...
2025-05-27 00:32:59,441 - api_logger - ERROR - Error generating audio: 'ElevenLabs' object has no attribute 'generate'
2025-05-27 00:32:59,441 - api_logger - ERROR - Failed to generate audio for question 2: 'ElevenLabs' object has no attribute 'generate'
2025-05-27 00:32:59,441 - api_logger - INFO - Audio generation completed. 0/2 files generated
2025-05-27 00:32:59,441 - api_logger - INFO - Updated job audio_job_b5ad520f25a7: status=partial, progress=0
2025-05-27 00:32:59,441 - api_logger - WARNING - Partial completion for job audio_job_b5ad520f25a7: 0/2 files
2025-05-27 00:42:20,379 - api_logger - INFO - Audio generation request received for 2 questions
2025-05-27 00:42:20,381 - api_logger - INFO - Created audio job: audio_job_4a9e4625f026
2025-05-27 00:42:20,381 - api_logger - INFO - Audio generation job created: audio_job_4a9e4625f026
2025-05-27 00:42:20,382 - api_logger - INFO - Starting background audio generation for job: audio_job_4a9e4625f026
2025-05-27 00:42:20,384 - api_logger - INFO - Updated job audio_job_4a9e4625f026: status=processing, progress=0
2025-05-27 00:42:20,384 - api_logger - INFO - Starting audio generation for 2 questions
2025-05-27 00:42:20,386 - api_logger - INFO - Generating audio for text: tell me about yourself...
2025-05-27 00:42:20,386 - api_logger - ERROR - Error generating audio: 'ElevenLabs' object has no attribute 'generate'
2025-05-27 00:42:20,388 - api_logger - ERROR - Failed to generate audio for question 1: 'ElevenLabs' object has no attribute 'generate'
2025-05-27 00:42:20,392 - api_logger - INFO - Generating audio for text: what is your strength...
2025-05-27 00:42:20,394 - api_logger - ERROR - Error generating audio: 'ElevenLabs' object has no attribute 'generate'
2025-05-27 00:42:20,394 - api_logger - ERROR - Failed to generate audio for question 2: 'ElevenLabs' object has no attribute 'generate'
2025-05-27 00:42:20,396 - api_logger - INFO - Audio generation completed. 0/2 files generated
2025-05-27 00:42:20,396 - api_logger - INFO - Updated job audio_job_4a9e4625f026: status=partial, progress=0
2025-05-27 00:42:20,396 - api_logger - WARNING - Partial completion for job audio_job_4a9e4625f026: 0/2 files
2025-05-27 00:51:17,041 - api_logger - INFO - Audio generation request received for 2 questions
2025-05-27 00:51:17,041 - api_logger - INFO - Created audio job: audio_job_8a19cd431343
2025-05-27 00:51:17,049 - api_logger - INFO - Audio generation job created: audio_job_8a19cd431343
2025-05-27 00:51:17,049 - api_logger - INFO - Starting background audio generation for job: audio_job_8a19cd431343
2025-05-27 00:51:17,051 - api_logger - INFO - Updated job audio_job_8a19cd431343: status=processing, progress=0
2025-05-27 00:51:17,051 - api_logger - INFO - Starting audio generation for 2 questions
2025-05-27 00:51:17,053 - api_logger - INFO - Generating audio for text: Tell me about yourself...
2025-05-27 00:51:17,753 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 00:51:17,777 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_8a19cd431343\question_1_fb7e9bfb.mp3
2025-05-27 00:51:17,780 - api_logger - INFO - Generated audio for question 1/2
2025-05-27 00:51:17,780 - api_logger - INFO - Generating audio for text: What are your strengths?...
2025-05-27 00:51:18,191 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 00:51:18,226 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_8a19cd431343\question_2_53a1dca0.mp3
2025-05-27 00:51:18,226 - api_logger - INFO - Generated audio for question 2/2
2025-05-27 00:51:18,229 - api_logger - INFO - Audio generation completed. 2/2 files generated
2025-05-27 00:51:18,229 - api_logger - INFO - Added audio file to job audio_job_8a19cd431343: /audio/download/audio_job_8a19cd431343/question_1_fb7e9bfb.mp3
2025-05-27 00:51:18,229 - api_logger - INFO - Added audio file to job audio_job_8a19cd431343: /audio/download/audio_job_8a19cd431343/question_2_53a1dca0.mp3
2025-05-27 00:51:18,229 - api_logger - INFO - Updated job audio_job_8a19cd431343: status=completed, progress=100
2025-05-27 00:51:18,231 - api_logger - INFO - Audio generation completed successfully for job: audio_job_8a19cd431343
2025-05-27 01:06:51,838 - api_logger - INFO - Audio generation request received for 2 questions
2025-05-27 01:06:51,846 - api_logger - INFO - Created audio job: audio_job_58462a583ea2
2025-05-27 01:06:51,846 - api_logger - INFO - Audio generation job created: audio_job_58462a583ea2
2025-05-27 01:06:51,846 - api_logger - INFO - Starting background audio generation for job: audio_job_58462a583ea2
2025-05-27 01:06:51,854 - api_logger - INFO - Updated job audio_job_58462a583ea2: status=processing, progress=0
2025-05-27 01:06:51,854 - api_logger - INFO - Starting audio generation for 2 questions
2025-05-27 01:06:51,854 - api_logger - INFO - Generating audio for text: tell me about yourself?...
2025-05-27 01:07:00,598 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 01:07:00,641 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_58462a583ea2\question_1_97364843.mp3
2025-05-27 01:07:00,641 - api_logger - INFO - Generated audio for question 1/2
2025-05-27 01:07:00,641 - api_logger - INFO - Generating audio for text: what is your strength?...
2025-05-27 01:07:01,106 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 01:07:01,163 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_58462a583ea2\question_2_3756cbf9.mp3
2025-05-27 01:07:01,163 - api_logger - INFO - Generated audio for question 2/2
2025-05-27 01:07:01,163 - api_logger - INFO - Audio generation completed. 2/2 files generated
2025-05-27 01:07:01,163 - api_logger - INFO - Added audio file to job audio_job_58462a583ea2: /audio/download/audio_job_58462a583ea2/question_1_97364843.mp3
2025-05-27 01:07:01,167 - api_logger - INFO - Added audio file to job audio_job_58462a583ea2: /audio/download/audio_job_58462a583ea2/question_2_3756cbf9.mp3
2025-05-27 01:07:01,167 - api_logger - INFO - Updated job audio_job_58462a583ea2: status=completed, progress=100
2025-05-27 01:07:01,167 - api_logger - INFO - Audio generation completed successfully for job: audio_job_58462a583ea2
2025-05-27 01:52:01,850 - api_logger - INFO - Audio generation request received for 3 questions
2025-05-27 01:52:01,855 - api_logger - INFO - Created audio job: audio_job_3e2e1f8f61a5
2025-05-27 01:52:01,858 - api_logger - INFO - Audio generation job created: audio_job_3e2e1f8f61a5
2025-05-27 01:52:01,862 - api_logger - INFO - Starting background audio generation for job: audio_job_3e2e1f8f61a5
2025-05-27 01:52:01,864 - api_logger - INFO - Updated job audio_job_3e2e1f8f61a5: status=processing, progress=0
2025-05-27 01:52:01,868 - api_logger - INFO - Starting audio generation for 3 questions
2025-05-27 01:52:01,869 - api_logger - INFO - Generating audio for text: tell me about yourself?...
2025-05-27 01:52:08,818 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/nPczCjzI2devNBz1zQrb "HTTP/1.1 200 OK"
2025-05-27 01:52:41,569 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_3e2e1f8f61a5\question_1_164b050f.mp3
2025-05-27 01:52:41,571 - api_logger - INFO - Generated audio for question 1/3
2025-05-27 01:52:41,571 - api_logger - INFO - Generating audio for text: what is the difference between generative AI and A...
2025-05-27 01:52:42,109 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/nPczCjzI2devNBz1zQrb "HTTP/1.1 200 OK"
2025-05-27 01:52:42,153 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_3e2e1f8f61a5\question_2_d7a975ad.mp3
2025-05-27 01:52:42,153 - api_logger - INFO - Generated audio for question 2/3
2025-05-27 01:52:42,154 - api_logger - INFO - Generating audio for text: what is your weakness?...
2025-05-27 01:52:42,557 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/nPczCjzI2devNBz1zQrb "HTTP/1.1 200 OK"
2025-05-27 01:52:42,567 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_3e2e1f8f61a5\question_3_f9e3a3d0.mp3
2025-05-27 01:52:42,567 - api_logger - INFO - Generated audio for question 3/3
2025-05-27 01:52:42,568 - api_logger - INFO - Audio generation completed. 3/3 files generated
2025-05-27 01:52:42,568 - api_logger - INFO - Added audio file to job audio_job_3e2e1f8f61a5: /audio/download/audio_job_3e2e1f8f61a5/question_1_164b050f.mp3
2025-05-27 01:52:42,568 - api_logger - INFO - Added audio file to job audio_job_3e2e1f8f61a5: /audio/download/audio_job_3e2e1f8f61a5/question_2_d7a975ad.mp3
2025-05-27 01:52:42,569 - api_logger - INFO - Added audio file to job audio_job_3e2e1f8f61a5: /audio/download/audio_job_3e2e1f8f61a5/question_3_f9e3a3d0.mp3
2025-05-27 01:52:42,569 - api_logger - INFO - Updated job audio_job_3e2e1f8f61a5: status=completed, progress=100
2025-05-27 01:52:42,569 - api_logger - INFO - Audio generation completed successfully for job: audio_job_3e2e1f8f61a5
2025-05-27 04:20:45,772 - api_logger - INFO - Request received for Interview Experience with Session ID: test-session-ved Model: gpt-3.5-turbo and is_ca: False
2025-05-27 04:20:51,153 - api_logger - INFO - Processing request for Interview Experience with Session ID: test-session-ved Model: gpt-4o-mini and is_ca: False
2025-05-27 04:20:51,153 - api_logger - INFO - Checking if all tasks are complete for session_id: test-session-ved
2025-05-27 04:20:51,396 - api_logger - INFO - Job status for session test-session-ved: [{'status': 'complete', 'request_type': 'job_title'}]
2025-05-27 04:20:51,396 - api_logger - INFO - Proceeding with tasks completion status for session ID: test-session-ved
2025-05-27 04:20:51,396 - api_logger - INFO - Processing interview for session_id: test-session-ved
2025-05-27 04:20:51,396 - api_logger - INFO - Fetching requests for session_id: test-session-ved
2025-05-27 04:20:51,647 - api_logger - INFO - Extracting questions from responses for session_id: test-session-ved
2025-05-27 04:20:51,647 - api_logger - INFO - Extracted 1 questions from responses.
2025-05-27 04:20:51,653 - api_logger - INFO - Fetching role and experience level for session_id: test-session-ved
2025-05-27 04:20:51,897 - api_logger - INFO - SQL result: [{'role': 'Data Scientist', 'experience_level': None, 'number_of_questions': 10}]
2025-05-27 04:20:51,897 - api_logger - INFO - Role: Data Scientist, Experience Level: None, Number of Questions: 10 for session_id: test-session-ved
2025-05-27 04:20:51,897 - api_logger - INFO - Creating interview experience messages.
2025-05-27 04:20:51,897 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-05-27 04:20:53,299 - api_logger - INFO - Interview experience messages created successfully.
2025-05-27 04:20:53,445 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-05-27 04:21:01,626 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 04:21:01,657 - api_logger - INFO - Response received successfully.
2025-05-27 04:21:01,657 - api_logger - INFO - Inserting record for session_id: test-session-ved, request_type: interview_experience
2025-05-27 04:21:02,128 - api_logger - INFO - Extracted questions for session_id: test-session-ved: [{'request_type': 'job_title', 'questions': ['What programming languages are you most proficient in, specifically for data analysis, and how have you used them in past projects?', 'Can you describe your experience with statistical analysis and the types of statistical tests you have employed in your work?', 'How do you approach data preprocessing and cleaning before analysis? Could you provide an example from your previous experience?', 'Which machine learning algorithms are you most familiar with, and can you walk us through a project where you implemented one of these algorithms?', 'Can you explain your experience with data visualization tools and how you use them to communicate your findings to non-technical stakeholders?', 'Describe a scenario where you utilized a specific data mining technique to solve a business problem. What was the outcome?', 'How do you ensure the accuracy and reliability of your data models? Could you share a specific example?', 'What experience do you have with big data technologies, such as Hadoop or Spark? How have you applied these in your work?', 'Could you elaborate on your understanding of A/B testing and your experience implementing it in your analyses?', 'Describe a project where you had to collaborate with a cross-functional team. How did you handle differing opinions and reach a consensus?', 'What is your experience with cloud computing platforms for data analysis, such as AWS or Azure?', "How do you stay updated with the latest trends and technologies in data science? Can you give an example of how you've implemented new knowledge in your work?", 'How do you prioritize competing tasks when working on multiple data science projects? Can you provide an example?', 'Can you describe your approach to mentoring or training team members who may be less experienced in data science skills?', 'What has been your most challenging data science project to date, and what did you learn from it?']}]
2025-05-27 04:21:02,128 - api_logger - INFO - Interview Experience Request with Session ID: test-session-ved Successfully processed
2025-05-27 04:23:22,752 - api_logger - INFO - Request received for Interview Experience with Session ID: test-session-ved Model: gpt-4o-mini and is_ca: False
2025-05-27 04:23:23,850 - api_logger - INFO - Processing request for Interview Experience with Session ID: test-session-ved Model: gpt-4o-mini and is_ca: False
2025-05-27 04:23:23,850 - api_logger - INFO - Checking if all tasks are complete for session_id: test-session-ved
2025-05-27 04:23:24,072 - api_logger - INFO - Job status for session test-session-ved: [{'status': 'complete', 'request_type': 'job_title'}]
2025-05-27 04:23:24,072 - api_logger - INFO - Proceeding with tasks completion status for session ID: test-session-ved
2025-05-27 04:23:24,072 - api_logger - INFO - Processing interview for session_id: test-session-ved
2025-05-27 04:23:24,073 - api_logger - INFO - Fetching requests for session_id: test-session-ved
2025-05-27 04:23:24,314 - api_logger - INFO - Extracting questions from responses for session_id: test-session-ved
2025-05-27 04:23:24,314 - api_logger - INFO - Extracted 1 questions from responses.
2025-05-27 04:23:24,314 - api_logger - INFO - Fetching role and experience level for session_id: test-session-ved
2025-05-27 04:23:24,549 - api_logger - INFO - SQL result: [{'role': 'Data Scientist', 'experience_level': None, 'number_of_questions': 10}]
2025-05-27 04:23:24,550 - api_logger - INFO - Role: Data Scientist, Experience Level: None, Number of Questions: 10 for session_id: test-session-ved
2025-05-27 04:23:24,550 - api_logger - INFO - Creating interview experience messages.
2025-05-27 04:23:24,550 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-05-27 04:23:25,864 - api_logger - INFO - Interview experience messages created successfully.
2025-05-27 04:23:25,934 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-05-27 04:23:30,805 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 04:23:30,836 - api_logger - INFO - Response received successfully.
2025-05-27 04:23:30,836 - api_logger - INFO - Inserting record for session_id: test-session-ved, request_type: interview_experience
2025-05-27 04:23:31,287 - api_logger - INFO - Extracted questions for session_id: test-session-ved: [{'request_type': 'job_title', 'questions': ['What programming languages are you most proficient in, specifically for data analysis, and how have you used them in past projects?', 'Can you describe your experience with statistical analysis and the types of statistical tests you have employed in your work?', 'How do you approach data preprocessing and cleaning before analysis? Could you provide an example from your previous experience?', 'Which machine learning algorithms are you most familiar with, and can you walk us through a project where you implemented one of these algorithms?', 'Can you explain your experience with data visualization tools and how you use them to communicate your findings to non-technical stakeholders?', 'Describe a scenario where you utilized a specific data mining technique to solve a business problem. What was the outcome?', 'How do you ensure the accuracy and reliability of your data models? Could you share a specific example?', 'What experience do you have with big data technologies, such as Hadoop or Spark? How have you applied these in your work?', 'Could you elaborate on your understanding of A/B testing and your experience implementing it in your analyses?', 'Describe a project where you had to collaborate with a cross-functional team. How did you handle differing opinions and reach a consensus?', 'What is your experience with cloud computing platforms for data analysis, such as AWS or Azure?', "How do you stay updated with the latest trends and technologies in data science? Can you give an example of how you've implemented new knowledge in your work?", 'How do you prioritize competing tasks when working on multiple data science projects? Can you provide an example?', 'Can you describe your approach to mentoring or training team members who may be less experienced in data science skills?', 'What has been your most challenging data science project to date, and what did you learn from it?']}]
2025-05-27 04:23:31,292 - api_logger - INFO - Interview Experience Request with Session ID: test-session-ved Successfully processed
2025-05-27 05:18:45,580 - api_logger - INFO - Audio generation request received for 2 questions
2025-05-27 05:18:45,580 - api_logger - INFO - Created audio job: audio_job_46bfe7914cbc
2025-05-27 05:18:45,580 - api_logger - INFO - Audio generation job created: audio_job_46bfe7914cbc
2025-05-27 05:18:45,580 - api_logger - INFO - Starting background audio generation for job: audio_job_46bfe7914cbc
2025-05-27 05:18:45,580 - api_logger - INFO - Updated job audio_job_46bfe7914cbc: status=processing, progress=0
2025-05-27 05:18:45,580 - api_logger - INFO - Starting audio generation for 2 questions
2025-05-27 05:18:45,580 - api_logger - INFO - Generating audio for text: Tell me about yourself...
2025-05-27 05:18:46,151 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 05:18:46,173 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_46bfe7914cbc\question_1_c6095411.mp3
2025-05-27 05:18:46,173 - api_logger - INFO - Generated audio for question 1/2
2025-05-27 05:18:46,173 - api_logger - INFO - Generating audio for text: What are your strengths?...
2025-05-27 05:18:46,576 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 05:18:46,586 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_46bfe7914cbc\question_2_30736264.mp3
2025-05-27 05:18:46,586 - api_logger - INFO - Generated audio for question 2/2
2025-05-27 05:18:46,586 - api_logger - INFO - Audio generation completed. 2/2 files generated
2025-05-27 05:18:46,587 - api_logger - INFO - Added audio file to job audio_job_46bfe7914cbc: /audio/download/audio_job_46bfe7914cbc/question_1_c6095411.mp3
2025-05-27 05:18:46,587 - api_logger - INFO - Added audio file to job audio_job_46bfe7914cbc: /audio/download/audio_job_46bfe7914cbc/question_2_30736264.mp3
2025-05-27 05:18:46,587 - api_logger - INFO - Updated job audio_job_46bfe7914cbc: status=completed, progress=100
2025-05-27 05:18:46,587 - api_logger - INFO - Audio generation completed successfully for job: audio_job_46bfe7914cbc
2025-05-27 05:20:23,805 - api_logger - INFO - Enhanced interview experience request for session test-session-ved with audio for 3 questions
2025-05-27 05:20:23,805 - api_logger - INFO - Request received for Interview Experience with Session ID: test-session-ved Model: gpt-4o-mini and is_ca: False
2025-05-27 05:20:25,019 - api_logger - INFO - Processing request for Interview Experience with Session ID: test-session-ved Model: gpt-4o-mini and is_ca: False
2025-05-27 05:20:25,019 - api_logger - INFO - Checking if all tasks are complete for session_id: test-session-ved
2025-05-27 05:20:25,258 - api_logger - INFO - Job status for session test-session-ved: [{'status': 'complete', 'request_type': 'job_title'}]
2025-05-27 05:20:25,258 - api_logger - INFO - Proceeding with tasks completion status for session ID: test-session-ved
2025-05-27 05:20:25,258 - api_logger - INFO - Processing interview for session_id: test-session-ved
2025-05-27 05:20:25,258 - api_logger - INFO - Fetching requests for session_id: test-session-ved
2025-05-27 05:20:25,520 - api_logger - INFO - Extracting questions from responses for session_id: test-session-ved
2025-05-27 05:20:25,520 - api_logger - INFO - Extracted 1 questions from responses.
2025-05-27 05:20:25,520 - api_logger - INFO - Fetching role and experience level for session_id: test-session-ved
2025-05-27 05:20:25,778 - api_logger - INFO - SQL result: [{'role': 'Data Scientist', 'experience_level': None, 'number_of_questions': 10}]
2025-05-27 05:20:25,778 - api_logger - INFO - Role: Data Scientist, Experience Level: None, Number of Questions: 10 for session_id: test-session-ved
2025-05-27 05:20:25,778 - api_logger - INFO - Creating interview experience messages.
2025-05-27 05:20:25,778 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-05-27 05:20:27,172 - api_logger - INFO - Interview experience messages created successfully.
2025-05-27 05:20:27,241 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-05-27 05:20:30,730 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 05:20:30,765 - api_logger - INFO - Response received successfully.
2025-05-27 05:20:30,765 - api_logger - INFO - Inserting record for session_id: test-session-ved, request_type: interview_experience
2025-05-27 05:20:31,246 - api_logger - INFO - Extracted questions for session_id: test-session-ved: [{'request_type': 'job_title', 'questions': ['What programming languages are you most proficient in, specifically for data analysis, and how have you used them in past projects?', 'Can you describe your experience with statistical analysis and the types of statistical tests you have employed in your work?', 'How do you approach data preprocessing and cleaning before analysis? Could you provide an example from your previous experience?', 'Which machine learning algorithms are you most familiar with, and can you walk us through a project where you implemented one of these algorithms?', 'Can you explain your experience with data visualization tools and how you use them to communicate your findings to non-technical stakeholders?', 'Describe a scenario where you utilized a specific data mining technique to solve a business problem. What was the outcome?', 'How do you ensure the accuracy and reliability of your data models? Could you share a specific example?', 'What experience do you have with big data technologies, such as Hadoop or Spark? How have you applied these in your work?', 'Could you elaborate on your understanding of A/B testing and your experience implementing it in your analyses?', 'Describe a project where you had to collaborate with a cross-functional team. How did you handle differing opinions and reach a consensus?', 'What is your experience with cloud computing platforms for data analysis, such as AWS or Azure?', "How do you stay updated with the latest trends and technologies in data science? Can you give an example of how you've implemented new knowledge in your work?", 'How do you prioritize competing tasks when working on multiple data science projects? Can you provide an example?', 'Can you describe your approach to mentoring or training team members who may be less experienced in data science skills?', 'What has been your most challenging data science project to date, and what did you learn from it?']}]
2025-05-27 05:20:31,248 - api_logger - INFO - Interview Experience Request with Session ID: test-session-ved Successfully processed
2025-05-27 05:20:31,248 - api_logger - INFO - Created audio job: audio_job_8e504f94386c
2025-05-27 05:20:31,248 - api_logger - INFO - Started audio generation job audio_job_8e504f94386c for 3 questions
2025-05-27 05:20:31,250 - api_logger - INFO - Starting background audio generation for job: audio_job_8e504f94386c
2025-05-27 05:20:31,250 - api_logger - INFO - Updated job audio_job_8e504f94386c: status=processing, progress=0
2025-05-27 05:20:31,250 - api_logger - INFO - Starting audio generation for 3 questions
2025-05-27 05:20:31,250 - api_logger - INFO - Generating audio for text: Can you tell me about yourself?...
2025-05-27 05:20:31,838 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 05:20:31,862 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_8e504f94386c\question_1_f8dbbe73.mp3
2025-05-27 05:20:31,863 - api_logger - INFO - Generated audio for question 1/3
2025-05-27 05:20:31,863 - api_logger - INFO - Generating audio for text: What programming languages are you most proficient...
2025-05-27 05:20:32,506 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 05:20:32,568 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_8e504f94386c\question_2_7e9f4f27.mp3
2025-05-27 05:20:32,568 - api_logger - INFO - Generated audio for question 2/3
2025-05-27 05:20:32,568 - api_logger - INFO - Generating audio for text: Can you describe your experience with statistical ...
2025-05-27 05:20:33,164 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 05:20:33,215 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_8e504f94386c\question_3_a041cc6a.mp3
2025-05-27 05:20:33,215 - api_logger - INFO - Generated audio for question 3/3
2025-05-27 05:20:33,215 - api_logger - INFO - Audio generation completed. 3/3 files generated
2025-05-27 05:20:33,215 - api_logger - INFO - Added audio file to job audio_job_8e504f94386c: /audio/download/audio_job_8e504f94386c/question_1_f8dbbe73.mp3
2025-05-27 05:20:33,216 - api_logger - INFO - Added audio file to job audio_job_8e504f94386c: /audio/download/audio_job_8e504f94386c/question_2_7e9f4f27.mp3
2025-05-27 05:20:33,216 - api_logger - INFO - Added audio file to job audio_job_8e504f94386c: /audio/download/audio_job_8e504f94386c/question_3_a041cc6a.mp3
2025-05-27 05:20:33,216 - api_logger - INFO - Updated job audio_job_8e504f94386c: status=completed, progress=100
2025-05-27 05:20:33,216 - api_logger - INFO - Audio generation completed successfully for job: audio_job_8e504f94386c
2025-05-27 05:24:39,096 - api_logger - WARNING - Unknown image_id: gUot1J0p7f1TAO8rUA9w, defaulting to Rachel
2025-05-27 05:24:39,096 - api_logger - INFO - Created audio job: audio_job_172922309cf2
2025-05-27 05:24:39,096 - api_logger - INFO - Starting background audio generation for job: audio_job_172922309cf2
2025-05-27 05:24:39,096 - api_logger - INFO - Updated job audio_job_172922309cf2: status=processing, progress=0
2025-05-27 05:24:39,096 - api_logger - INFO - Starting audio generation for 1 questions
2025-05-27 05:24:39,096 - api_logger - INFO - Generating audio for text: string...
2025-05-27 05:24:39,576 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 05:24:39,582 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_172922309cf2\question_1_419f2b79.mp3
2025-05-27 05:24:39,583 - api_logger - INFO - Generated audio for question 1/1
2025-05-27 05:24:39,583 - api_logger - INFO - Audio generation completed. 1/1 files generated
2025-05-27 05:24:39,583 - api_logger - INFO - Added audio file to job audio_job_172922309cf2: /audio/download/audio_job_172922309cf2/question_1_419f2b79.mp3
2025-05-27 05:24:39,583 - api_logger - INFO - Updated job audio_job_172922309cf2: status=completed, progress=100
2025-05-27 05:24:39,583 - api_logger - INFO - Audio generation completed successfully for job: audio_job_172922309cf2
2025-05-27 05:28:43,044 - api_logger - INFO - Audio generation request received for 3 questions
2025-05-27 05:28:43,048 - api_logger - INFO - Created audio job: audio_job_713e6c3e573a
2025-05-27 05:28:43,049 - api_logger - INFO - Audio generation job created: audio_job_713e6c3e573a
2025-05-27 05:28:43,049 - api_logger - INFO - Starting background audio generation for job: audio_job_713e6c3e573a
2025-05-27 05:28:43,049 - api_logger - INFO - Updated job audio_job_713e6c3e573a: status=processing, progress=0
2025-05-27 05:28:43,051 - api_logger - INFO - Starting audio generation for 3 questions
2025-05-27 05:28:43,051 - api_logger - INFO - Generating audio for text: Tell me about yourself and your background...
2025-05-27 05:28:43,597 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 05:28:43,622 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_713e6c3e573a\question_1_814713b8.mp3
2025-05-27 05:28:43,623 - api_logger - INFO - Generated audio for question 1/3
2025-05-27 05:28:43,623 - api_logger - INFO - Generating audio for text: What are your greatest strengths as a professional...
2025-05-27 05:28:44,110 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 05:28:44,128 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_713e6c3e573a\question_2_47290ab3.mp3
2025-05-27 05:28:44,128 - api_logger - INFO - Generated audio for question 2/3
2025-05-27 05:28:44,129 - api_logger - INFO - Generating audio for text: Describe a challenging project you worked on...
2025-05-27 05:28:44,770 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 05:28:44,798 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_713e6c3e573a\question_3_01f6ce6d.mp3
2025-05-27 05:28:44,799 - api_logger - INFO - Generated audio for question 3/3
2025-05-27 05:28:44,799 - api_logger - INFO - Audio generation completed. 3/3 files generated
2025-05-27 05:28:44,799 - api_logger - INFO - Added audio file to job audio_job_713e6c3e573a: /audio/download/audio_job_713e6c3e573a/question_1_814713b8.mp3
2025-05-27 05:28:44,799 - api_logger - INFO - Added audio file to job audio_job_713e6c3e573a: /audio/download/audio_job_713e6c3e573a/question_2_47290ab3.mp3
2025-05-27 05:28:44,799 - api_logger - INFO - Added audio file to job audio_job_713e6c3e573a: /audio/download/audio_job_713e6c3e573a/question_3_01f6ce6d.mp3
2025-05-27 05:28:44,799 - api_logger - INFO - Updated job audio_job_713e6c3e573a: status=completed, progress=100
2025-05-27 05:28:44,799 - api_logger - INFO - Audio generation completed successfully for job: audio_job_713e6c3e573a
2025-05-27 05:28:53,194 - api_logger - INFO - Created audio job: audio_job_1db35f8ad05f
2025-05-27 05:28:53,194 - api_logger - INFO - Starting background audio generation for job: audio_job_1db35f8ad05f
2025-05-27 05:28:53,194 - api_logger - INFO - Updated job audio_job_1db35f8ad05f: status=processing, progress=0
2025-05-27 05:28:53,194 - api_logger - INFO - Starting audio generation for 1 questions
2025-05-27 05:28:53,200 - api_logger - INFO - Generating audio for text: What motivates you in your career?...
2025-05-27 05:28:53,724 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 05:28:53,750 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_1db35f8ad05f\question_1_105b643d.mp3
2025-05-27 05:28:53,750 - api_logger - INFO - Generated audio for question 1/1
2025-05-27 05:28:53,750 - api_logger - INFO - Audio generation completed. 1/1 files generated
2025-05-27 05:28:53,750 - api_logger - INFO - Added audio file to job audio_job_1db35f8ad05f: /audio/download/audio_job_1db35f8ad05f/question_1_105b643d.mp3
2025-05-27 05:28:53,750 - api_logger - INFO - Updated job audio_job_1db35f8ad05f: status=completed, progress=100
2025-05-27 05:28:53,750 - api_logger - INFO - Audio generation completed successfully for job: audio_job_1db35f8ad05f
2025-05-27 05:28:55,215 - api_logger - INFO - Created audio job: audio_job_3f70eb9f22f5
2025-05-27 05:28:55,215 - api_logger - INFO - Starting background audio generation for job: audio_job_3f70eb9f22f5
2025-05-27 05:28:55,215 - api_logger - INFO - Updated job audio_job_3f70eb9f22f5: status=processing, progress=0
2025-05-27 05:28:55,224 - api_logger - INFO - Starting audio generation for 1 questions
2025-05-27 05:28:55,224 - api_logger - INFO - Generating audio for text: Describe your leadership style...
2025-05-27 05:28:55,693 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/zbj5pYu7PWmTR3zNpMct "HTTP/1.1 400 Bad Request"
2025-05-27 05:28:55,694 - api_logger - ERROR - Error generating audio: headers: {'date': 'Tue, 27 May 2025 12:28:57 GMT', 'server': 'uvicorn', 'content-length': '191', 'content-type': 'application/json', 'access-control-allow-origin': '*', 'access-control-allow-headers': '*', 'access-control-allow-methods': 'POST, PATCH, OPTIONS, DELETE, GET, PUT', 'access-control-max-age': '600', 'strict-transport-security': 'max-age=31536000; includeSubDomains', 'x-trace-id': 'ea0f059531803429b42768a070600db8', 'via': '1.1 google, 1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'}, status_code: 400, body: {'detail': {'status': 'voice_limit_reached', 'message': 'You have reached your maximum amount of custom voices (30 / 30). You can upgrade your subscription to increase your custom voice limit.'}}
2025-05-27 05:28:55,694 - api_logger - ERROR - Failed to generate audio for question 1: headers: {'date': 'Tue, 27 May 2025 12:28:57 GMT', 'server': 'uvicorn', 'content-length': '191', 'content-type': 'application/json', 'access-control-allow-origin': '*', 'access-control-allow-headers': '*', 'access-control-allow-methods': 'POST, PATCH, OPTIONS, DELETE, GET, PUT', 'access-control-max-age': '600', 'strict-transport-security': 'max-age=31536000; includeSubDomains', 'x-trace-id': 'ea0f059531803429b42768a070600db8', 'via': '1.1 google, 1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'}, status_code: 400, body: {'detail': {'status': 'voice_limit_reached', 'message': 'You have reached your maximum amount of custom voices (30 / 30). You can upgrade your subscription to increase your custom voice limit.'}}
2025-05-27 05:28:55,694 - api_logger - INFO - Audio generation completed. 0/1 files generated
2025-05-27 05:28:55,694 - api_logger - INFO - Updated job audio_job_3f70eb9f22f5: status=partial, progress=0
2025-05-27 05:28:55,694 - api_logger - WARNING - Partial completion for job audio_job_3f70eb9f22f5: 0/1 files
2025-05-27 05:28:57,235 - api_logger - INFO - Created audio job: audio_job_93961ddc2175
2025-05-27 05:28:57,235 - api_logger - INFO - Starting background audio generation for job: audio_job_93961ddc2175
2025-05-27 05:28:57,235 - api_logger - INFO - Updated job audio_job_93961ddc2175: status=processing, progress=0
2025-05-27 05:28:57,239 - api_logger - INFO - Starting audio generation for 1 questions
2025-05-27 05:28:57,239 - api_logger - INFO - Generating audio for text: How do you handle stress?...
2025-05-27 05:28:57,648 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/nPczCjzI2devNBz1zQrb "HTTP/1.1 200 OK"
2025-05-27 05:28:57,667 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_93961ddc2175\question_1_5e885595.mp3
2025-05-27 05:28:57,667 - api_logger - INFO - Generated audio for question 1/1
2025-05-27 05:28:57,668 - api_logger - INFO - Audio generation completed. 1/1 files generated
2025-05-27 05:28:57,668 - api_logger - INFO - Added audio file to job audio_job_93961ddc2175: /audio/download/audio_job_93961ddc2175/question_1_5e885595.mp3
2025-05-27 05:28:57,668 - api_logger - INFO - Updated job audio_job_93961ddc2175: status=completed, progress=100
2025-05-27 05:28:57,668 - api_logger - INFO - Audio generation completed successfully for job: audio_job_93961ddc2175
2025-05-27 05:28:59,265 - api_logger - INFO - Enhanced interview experience request for session api_test_413b4955 with audio for 2 questions
2025-05-27 05:28:59,265 - api_logger - INFO - Request received for Interview Experience with Session ID: api_test_413b4955 Model: gpt-4o-mini and is_ca: False
2025-05-27 05:29:00,471 - api_logger - INFO - Processing request for Interview Experience with Session ID: api_test_413b4955 Model: gpt-4o-mini and is_ca: False
2025-05-27 05:29:00,471 - api_logger - INFO - Checking if all tasks are complete for session_id: api_test_413b4955
2025-05-27 05:29:00,709 - api_logger - INFO - Proceeding with tasks completion status for session ID: api_test_413b4955
2025-05-27 05:29:00,709 - api_logger - INFO - Processing interview for session_id: api_test_413b4955
2025-05-27 05:29:00,709 - api_logger - INFO - Fetching requests for session_id: api_test_413b4955
2025-05-27 05:29:00,972 - api_logger - INFO - Extracting questions from responses for session_id: api_test_413b4955
2025-05-27 05:29:00,972 - api_logger - INFO - Extracted 0 questions from responses.
2025-05-27 05:29:00,972 - api_logger - INFO - Fetching role and experience level for session_id: api_test_413b4955
2025-05-27 05:29:01,228 - api_logger - INFO - SQL result: ()
2025-05-27 05:29:01,228 - api_logger - INFO - Creating interview experience messages.
2025-05-27 05:29:01,228 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-05-27 05:29:02,550 - api_logger - INFO - Interview experience messages created successfully.
2025-05-27 05:29:02,618 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-05-27 05:29:06,098 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 05:29:06,129 - api_logger - INFO - Response received successfully.
2025-05-27 05:29:06,129 - api_logger - INFO - Inserting record for session_id: api_test_413b4955, request_type: interview_experience
2025-05-27 05:29:06,619 - api_logger - INFO - Extracted questions for session_id: api_test_413b4955: []
2025-05-27 05:29:06,619 - api_logger - INFO - Interview Experience Request with Session ID: api_test_413b4955 Successfully processed
2025-05-27 05:29:06,619 - api_logger - INFO - Created audio job: audio_job_9810bf1d1846
2025-05-27 05:29:06,620 - api_logger - INFO - Started audio generation job audio_job_9810bf1d1846 for 2 questions
2025-05-27 05:29:06,621 - api_logger - INFO - Starting background audio generation for job: audio_job_9810bf1d1846
2025-05-27 05:29:06,621 - api_logger - INFO - Updated job audio_job_9810bf1d1846: status=processing, progress=0
2025-05-27 05:29:06,621 - api_logger - INFO - Starting audio generation for 2 questions
2025-05-27 05:29:06,621 - api_logger - INFO - Generating audio for text: Can you tell me a little about yourself?...
2025-05-27 05:29:07,143 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 05:29:07,171 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_9810bf1d1846\question_1_4d473f87.mp3
2025-05-27 05:29:07,171 - api_logger - INFO - Generated audio for question 1/2
2025-05-27 05:29:07,171 - api_logger - INFO - Generating audio for text: What inspired you to apply for this position?...
2025-05-27 05:29:07,702 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 05:29:07,718 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_9810bf1d1846\question_2_8b88ada2.mp3
2025-05-27 05:29:07,718 - api_logger - INFO - Generated audio for question 2/2
2025-05-27 05:29:07,718 - api_logger - INFO - Audio generation completed. 2/2 files generated
2025-05-27 05:29:07,719 - api_logger - INFO - Added audio file to job audio_job_9810bf1d1846: /audio/download/audio_job_9810bf1d1846/question_1_4d473f87.mp3
2025-05-27 05:29:07,719 - api_logger - INFO - Added audio file to job audio_job_9810bf1d1846: /audio/download/audio_job_9810bf1d1846/question_2_8b88ada2.mp3
2025-05-27 05:29:07,719 - api_logger - INFO - Updated job audio_job_9810bf1d1846: status=completed, progress=100
2025-05-27 05:29:07,719 - api_logger - INFO - Audio generation completed successfully for job: audio_job_9810bf1d1846
2025-05-27 05:29:20,484 - api_logger - INFO - Audio generation request received for 3 questions
2025-05-27 05:29:20,484 - api_logger - INFO - Created audio job: audio_job_f57226c3df5d
2025-05-27 05:29:20,484 - api_logger - INFO - Audio generation job created: audio_job_f57226c3df5d
2025-05-27 05:29:20,486 - api_logger - INFO - Starting background audio generation for job: audio_job_f57226c3df5d
2025-05-27 05:29:20,486 - api_logger - INFO - Updated job audio_job_f57226c3df5d: status=processing, progress=0
2025-05-27 05:29:20,486 - api_logger - INFO - Starting audio generation for 3 questions
2025-05-27 05:29:20,486 - api_logger - INFO - Generating audio for text: Tell me about yourself and your background...
2025-05-27 05:29:21,062 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 05:29:21,107 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_f57226c3df5d\question_1_6354b562.mp3
2025-05-27 05:29:21,107 - api_logger - INFO - Generated audio for question 1/3
2025-05-27 05:29:21,107 - api_logger - INFO - Generating audio for text: What are your greatest strengths as a professional...
2025-05-27 05:29:23,066 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 05:29:23,086 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_f57226c3df5d\question_2_8a459022.mp3
2025-05-27 05:29:23,086 - api_logger - INFO - Generated audio for question 2/3
2025-05-27 05:29:23,086 - api_logger - INFO - Generating audio for text: Describe a challenging project you worked on...
2025-05-27 05:29:23,670 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 05:29:23,676 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_f57226c3df5d\question_3_7e832822.mp3
2025-05-27 05:29:23,676 - api_logger - INFO - Generated audio for question 3/3
2025-05-27 05:29:23,676 - api_logger - INFO - Audio generation completed. 3/3 files generated
2025-05-27 05:29:23,677 - api_logger - INFO - Added audio file to job audio_job_f57226c3df5d: /audio/download/audio_job_f57226c3df5d/question_1_6354b562.mp3
2025-05-27 05:29:23,677 - api_logger - INFO - Added audio file to job audio_job_f57226c3df5d: /audio/download/audio_job_f57226c3df5d/question_2_8a459022.mp3
2025-05-27 05:29:23,677 - api_logger - INFO - Added audio file to job audio_job_f57226c3df5d: /audio/download/audio_job_f57226c3df5d/question_3_7e832822.mp3
2025-05-27 05:29:23,677 - api_logger - INFO - Updated job audio_job_f57226c3df5d: status=completed, progress=100
2025-05-27 05:29:23,678 - api_logger - INFO - Audio generation completed successfully for job: audio_job_f57226c3df5d
2025-05-27 05:29:36,271 - api_logger - INFO - Created audio job: audio_job_2b998005af09
2025-05-27 05:29:36,271 - api_logger - INFO - Starting background audio generation for job: audio_job_2b998005af09
2025-05-27 05:29:36,271 - api_logger - INFO - Updated job audio_job_2b998005af09: status=processing, progress=0
2025-05-27 05:29:36,271 - api_logger - INFO - Starting audio generation for 1 questions
2025-05-27 05:29:36,271 - api_logger - INFO - Generating audio for text: What motivates you in your career?...
2025-05-27 05:29:36,786 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 05:29:36,812 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_2b998005af09\question_1_5772f951.mp3
2025-05-27 05:29:36,812 - api_logger - INFO - Generated audio for question 1/1
2025-05-27 05:29:36,812 - api_logger - INFO - Audio generation completed. 1/1 files generated
2025-05-27 05:29:36,812 - api_logger - INFO - Added audio file to job audio_job_2b998005af09: /audio/download/audio_job_2b998005af09/question_1_5772f951.mp3
2025-05-27 05:29:36,812 - api_logger - INFO - Updated job audio_job_2b998005af09: status=completed, progress=100
2025-05-27 05:29:36,812 - api_logger - INFO - Audio generation completed successfully for job: audio_job_2b998005af09
2025-05-27 05:29:38,319 - api_logger - INFO - Created audio job: audio_job_55c10291ea4f
2025-05-27 05:29:38,319 - api_logger - INFO - Starting background audio generation for job: audio_job_55c10291ea4f
2025-05-27 05:29:38,319 - api_logger - INFO - Updated job audio_job_55c10291ea4f: status=processing, progress=0
2025-05-27 05:29:38,319 - api_logger - INFO - Starting audio generation for 1 questions
2025-05-27 05:29:38,319 - api_logger - INFO - Generating audio for text: Describe your leadership style...
2025-05-27 05:29:38,741 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/zbj5pYu7PWmTR3zNpMct "HTTP/1.1 400 Bad Request"
2025-05-27 05:29:38,741 - api_logger - ERROR - Error generating audio: headers: {'date': 'Tue, 27 May 2025 12:29:40 GMT', 'server': 'uvicorn', 'content-length': '191', 'content-type': 'application/json', 'access-control-allow-origin': '*', 'access-control-allow-headers': '*', 'access-control-allow-methods': 'POST, PATCH, OPTIONS, DELETE, GET, PUT', 'access-control-max-age': '600', 'strict-transport-security': 'max-age=31536000; includeSubDomains', 'x-trace-id': 'e1340b8fae0461797bdbe6675773d9e1', 'via': '1.1 google, 1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'}, status_code: 400, body: {'detail': {'status': 'voice_limit_reached', 'message': 'You have reached your maximum amount of custom voices (30 / 30). You can upgrade your subscription to increase your custom voice limit.'}}
2025-05-27 05:29:38,741 - api_logger - ERROR - Failed to generate audio for question 1: headers: {'date': 'Tue, 27 May 2025 12:29:40 GMT', 'server': 'uvicorn', 'content-length': '191', 'content-type': 'application/json', 'access-control-allow-origin': '*', 'access-control-allow-headers': '*', 'access-control-allow-methods': 'POST, PATCH, OPTIONS, DELETE, GET, PUT', 'access-control-max-age': '600', 'strict-transport-security': 'max-age=31536000; includeSubDomains', 'x-trace-id': 'e1340b8fae0461797bdbe6675773d9e1', 'via': '1.1 google, 1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'}, status_code: 400, body: {'detail': {'status': 'voice_limit_reached', 'message': 'You have reached your maximum amount of custom voices (30 / 30). You can upgrade your subscription to increase your custom voice limit.'}}
2025-05-27 05:29:38,743 - api_logger - INFO - Audio generation completed. 0/1 files generated
2025-05-27 05:29:38,743 - api_logger - INFO - Updated job audio_job_55c10291ea4f: status=partial, progress=0
2025-05-27 05:29:38,743 - api_logger - WARNING - Partial completion for job audio_job_55c10291ea4f: 0/1 files
2025-05-27 05:29:40,350 - api_logger - INFO - Created audio job: audio_job_b9e0d3d90835
2025-05-27 05:29:40,350 - api_logger - INFO - Starting background audio generation for job: audio_job_b9e0d3d90835
2025-05-27 05:29:40,350 - api_logger - INFO - Updated job audio_job_b9e0d3d90835: status=processing, progress=0
2025-05-27 05:29:40,350 - api_logger - INFO - Starting audio generation for 1 questions
2025-05-27 05:29:40,350 - api_logger - INFO - Generating audio for text: How do you handle stress?...
2025-05-27 05:29:40,750 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/nPczCjzI2devNBz1zQrb "HTTP/1.1 200 OK"
2025-05-27 05:29:40,762 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_b9e0d3d90835\question_1_ad80d62f.mp3
2025-05-27 05:29:40,762 - api_logger - INFO - Generated audio for question 1/1
2025-05-27 05:29:40,762 - api_logger - INFO - Audio generation completed. 1/1 files generated
2025-05-27 05:29:40,762 - api_logger - INFO - Added audio file to job audio_job_b9e0d3d90835: /audio/download/audio_job_b9e0d3d90835/question_1_ad80d62f.mp3
2025-05-27 05:29:40,762 - api_logger - INFO - Updated job audio_job_b9e0d3d90835: status=completed, progress=100
2025-05-27 05:29:40,762 - api_logger - INFO - Audio generation completed successfully for job: audio_job_b9e0d3d90835
2025-05-27 05:29:42,387 - api_logger - INFO - Enhanced interview experience request for session api_test_06a0a80e with audio for 2 questions
2025-05-27 05:29:42,387 - api_logger - INFO - Request received for Interview Experience with Session ID: api_test_06a0a80e Model: gpt-4o-mini and is_ca: False
2025-05-27 05:29:43,465 - api_logger - INFO - Processing request for Interview Experience with Session ID: api_test_06a0a80e Model: gpt-4o-mini and is_ca: False
2025-05-27 05:29:43,465 - api_logger - INFO - Checking if all tasks are complete for session_id: api_test_06a0a80e
2025-05-27 05:29:43,686 - api_logger - INFO - Proceeding with tasks completion status for session ID: api_test_06a0a80e
2025-05-27 05:29:43,686 - api_logger - INFO - Processing interview for session_id: api_test_06a0a80e
2025-05-27 05:29:43,686 - api_logger - INFO - Fetching requests for session_id: api_test_06a0a80e
2025-05-27 05:29:43,921 - api_logger - INFO - Extracting questions from responses for session_id: api_test_06a0a80e
2025-05-27 05:29:43,921 - api_logger - INFO - Extracted 0 questions from responses.
2025-05-27 05:29:43,921 - api_logger - INFO - Fetching role and experience level for session_id: api_test_06a0a80e
2025-05-27 05:29:44,154 - api_logger - INFO - SQL result: ()
2025-05-27 05:29:44,155 - api_logger - INFO - Creating interview experience messages.
2025-05-27 05:29:44,155 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-05-27 05:29:45,481 - api_logger - INFO - Interview experience messages created successfully.
2025-05-27 05:29:45,482 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-05-27 05:29:49,059 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 05:29:49,062 - api_logger - INFO - Response received successfully.
2025-05-27 05:29:49,062 - api_logger - INFO - Inserting record for session_id: api_test_06a0a80e, request_type: interview_experience
2025-05-27 05:29:49,500 - api_logger - INFO - Extracted questions for session_id: api_test_06a0a80e: []
2025-05-27 05:29:49,500 - api_logger - INFO - Interview Experience Request with Session ID: api_test_06a0a80e Successfully processed
2025-05-27 05:29:49,507 - api_logger - INFO - Created audio job: audio_job_e04c9bcf4135
2025-05-27 05:29:49,507 - api_logger - INFO - Started audio generation job audio_job_e04c9bcf4135 for 2 questions
2025-05-27 05:29:49,509 - api_logger - INFO - Starting background audio generation for job: audio_job_e04c9bcf4135
2025-05-27 05:29:49,509 - api_logger - INFO - Updated job audio_job_e04c9bcf4135: status=processing, progress=0
2025-05-27 05:29:49,509 - api_logger - INFO - Starting audio generation for 2 questions
2025-05-27 05:29:49,509 - api_logger - INFO - Generating audio for text: Can you tell me a little about yourself?...
2025-05-27 05:29:50,041 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 05:29:50,070 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_e04c9bcf4135\question_1_b116606e.mp3
2025-05-27 05:29:50,070 - api_logger - INFO - Generated audio for question 1/2
2025-05-27 05:29:50,070 - api_logger - INFO - Generating audio for text: What attracted you to this position?...
2025-05-27 05:29:50,541 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 05:29:50,558 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_e04c9bcf4135\question_2_08a39c15.mp3
2025-05-27 05:29:50,558 - api_logger - INFO - Generated audio for question 2/2
2025-05-27 05:29:50,558 - api_logger - INFO - Audio generation completed. 2/2 files generated
2025-05-27 05:29:50,558 - api_logger - INFO - Added audio file to job audio_job_e04c9bcf4135: /audio/download/audio_job_e04c9bcf4135/question_1_b116606e.mp3
2025-05-27 05:29:50,558 - api_logger - INFO - Added audio file to job audio_job_e04c9bcf4135: /audio/download/audio_job_e04c9bcf4135/question_2_08a39c15.mp3
2025-05-27 05:29:50,558 - api_logger - INFO - Updated job audio_job_e04c9bcf4135: status=completed, progress=100
2025-05-27 05:29:50,559 - api_logger - INFO - Audio generation completed successfully for job: audio_job_e04c9bcf4135
2025-05-27 05:36:56,007 - api_logger - INFO - Enhanced interview experience request for session test-session-ved with audio for 3 questions
2025-05-27 05:36:56,007 - api_logger - INFO - Request received for Interview Experience with Session ID: test-session-ved Model: gpt-4o-mini and is_ca: False
2025-05-27 05:36:57,201 - api_logger - INFO - Processing request for Interview Experience with Session ID: test-session-ved Model: gpt-4o-mini and is_ca: False
2025-05-27 05:36:57,201 - api_logger - INFO - Checking if all tasks are complete for session_id: test-session-ved
2025-05-27 05:36:57,432 - api_logger - INFO - Job status for session test-session-ved: [{'status': 'complete', 'request_type': 'job_title'}]
2025-05-27 05:36:57,432 - api_logger - INFO - Proceeding with tasks completion status for session ID: test-session-ved
2025-05-27 05:36:57,432 - api_logger - INFO - Processing interview for session_id: test-session-ved
2025-05-27 05:36:57,432 - api_logger - INFO - Fetching requests for session_id: test-session-ved
2025-05-27 05:36:57,689 - api_logger - INFO - Extracting questions from responses for session_id: test-session-ved
2025-05-27 05:36:57,694 - api_logger - INFO - Extracted 1 questions from responses.
2025-05-27 05:36:57,694 - api_logger - INFO - Fetching role and experience level for session_id: test-session-ved
2025-05-27 05:36:57,944 - api_logger - INFO - SQL result: [{'role': 'Data Scientist', 'experience_level': None, 'number_of_questions': 10}]
2025-05-27 05:36:57,944 - api_logger - INFO - Role: Data Scientist, Experience Level: None, Number of Questions: 10 for session_id: test-session-ved
2025-05-27 05:36:57,944 - api_logger - INFO - Creating interview experience messages.
2025-05-27 05:36:57,944 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-05-27 05:36:59,341 - api_logger - INFO - Interview experience messages created successfully.
2025-05-27 05:36:59,417 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-05-27 05:37:07,151 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 05:37:07,181 - api_logger - INFO - Response received successfully.
2025-05-27 05:37:07,181 - api_logger - INFO - Inserting record for session_id: test-session-ved, request_type: interview_experience
2025-05-27 05:37:07,661 - api_logger - INFO - Extracted questions for session_id: test-session-ved: [{'request_type': 'job_title', 'questions': ['What programming languages are you most proficient in, specifically for data analysis, and how have you used them in past projects?', 'Can you describe your experience with statistical analysis and the types of statistical tests you have employed in your work?', 'How do you approach data preprocessing and cleaning before analysis? Could you provide an example from your previous experience?', 'Which machine learning algorithms are you most familiar with, and can you walk us through a project where you implemented one of these algorithms?', 'Can you explain your experience with data visualization tools and how you use them to communicate your findings to non-technical stakeholders?', 'Describe a scenario where you utilized a specific data mining technique to solve a business problem. What was the outcome?', 'How do you ensure the accuracy and reliability of your data models? Could you share a specific example?', 'What experience do you have with big data technologies, such as Hadoop or Spark? How have you applied these in your work?', 'Could you elaborate on your understanding of A/B testing and your experience implementing it in your analyses?', 'Describe a project where you had to collaborate with a cross-functional team. How did you handle differing opinions and reach a consensus?', 'What is your experience with cloud computing platforms for data analysis, such as AWS or Azure?', "How do you stay updated with the latest trends and technologies in data science? Can you give an example of how you've implemented new knowledge in your work?", 'How do you prioritize competing tasks when working on multiple data science projects? Can you provide an example?', 'Can you describe your approach to mentoring or training team members who may be less experienced in data science skills?', 'What has been your most challenging data science project to date, and what did you learn from it?']}]
2025-05-27 05:37:07,661 - api_logger - INFO - Interview Experience Request with Session ID: test-session-ved Successfully processed
2025-05-27 05:37:07,662 - api_logger - INFO - Created audio job: audio_job_756c458f0728
2025-05-27 05:37:07,662 - api_logger - INFO - Started audio generation job audio_job_756c458f0728 for 3 questions
2025-05-27 05:37:07,662 - api_logger - INFO - Starting background audio generation for job: audio_job_756c458f0728
2025-05-27 05:37:07,662 - api_logger - INFO - Updated job audio_job_756c458f0728: status=processing, progress=0
2025-05-27 05:37:07,662 - api_logger - INFO - Starting audio generation for 3 questions
2025-05-27 05:37:07,662 - api_logger - INFO - Generating audio for text: Tell me about yourself and your background in data...
2025-05-27 05:37:08,244 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 05:37:08,286 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_756c458f0728\question_1_fc1e7599.mp3
2025-05-27 05:37:08,286 - api_logger - INFO - Generated audio for question 1/3
2025-05-27 05:37:08,286 - api_logger - INFO - Generating audio for text: What programming languages are you most proficient...
2025-05-27 05:37:08,924 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 05:37:08,964 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_756c458f0728\question_2_15f5bb26.mp3
2025-05-27 05:37:08,964 - api_logger - INFO - Generated audio for question 2/3
2025-05-27 05:37:08,965 - api_logger - INFO - Generating audio for text: Can you describe your experience with statistical ...
2025-05-27 05:37:09,594 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 05:37:09,639 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_756c458f0728\question_3_47f3f74c.mp3
2025-05-27 05:37:09,639 - api_logger - INFO - Generated audio for question 3/3
2025-05-27 05:37:09,639 - api_logger - INFO - Audio generation completed. 3/3 files generated
2025-05-27 05:37:09,639 - api_logger - INFO - Added audio file to job audio_job_756c458f0728: /audio/download/audio_job_756c458f0728/question_1_fc1e7599.mp3
2025-05-27 05:37:09,639 - api_logger - INFO - Added audio file to job audio_job_756c458f0728: /audio/download/audio_job_756c458f0728/question_2_15f5bb26.mp3
2025-05-27 05:37:09,639 - api_logger - INFO - Added audio file to job audio_job_756c458f0728: /audio/download/audio_job_756c458f0728/question_3_47f3f74c.mp3
2025-05-27 05:37:09,640 - api_logger - INFO - Updated job audio_job_756c458f0728: status=completed, progress=100
2025-05-27 05:37:09,640 - api_logger - INFO - Audio generation completed successfully for job: audio_job_756c458f0728
2025-05-27 11:41:13,398 - api_logger - INFO - GET /audio/voices endpoint called
2025-05-27 11:41:13,399 - api_logger - INFO - Returning voices response with 5 voices
2025-05-27 11:44:30,268 - api_logger - INFO - GET /audio/voices endpoint called
2025-05-27 11:44:30,271 - api_logger - INFO - Returning voices response with 5 voices
2025-05-27 11:48:57,015 - api_logger - INFO - Request received for Interview Experience with Session ID: test-session-ved Model: gpt-4o-mini and is_ca: False
2025-05-27 11:48:58,144 - api_logger - INFO - Processing request for Interview Experience with Session ID: test-session-ved Model: gpt-4o-mini and is_ca: False
2025-05-27 11:48:58,149 - api_logger - INFO - Checking if all tasks are complete for session_id: test-session-ved
2025-05-27 11:48:58,371 - api_logger - INFO - Job status for session test-session-ved: [{'status': 'complete', 'request_type': 'job_title'}]
2025-05-27 11:48:58,372 - api_logger - INFO - Proceeding with tasks completion status for session ID: test-session-ved
2025-05-27 11:48:58,372 - api_logger - INFO - Processing interview for session_id: test-session-ved
2025-05-27 11:48:58,372 - api_logger - INFO - Fetching requests for session_id: test-session-ved
2025-05-27 11:48:58,624 - api_logger - INFO - Extracting questions from responses for session_id: test-session-ved
2025-05-27 11:48:58,624 - api_logger - INFO - Extracted 1 questions from responses.
2025-05-27 11:48:58,624 - api_logger - INFO - Fetching role and experience level for session_id: test-session-ved
2025-05-27 11:48:58,863 - api_logger - INFO - SQL result: [{'role': 'Data Scientist', 'experience_level': None, 'number_of_questions': 10}]
2025-05-27 11:48:58,863 - api_logger - INFO - Role: Data Scientist, Experience Level: None, Number of Questions: 10 for session_id: test-session-ved
2025-05-27 11:48:58,864 - api_logger - INFO - Creating interview experience messages.
2025-05-27 11:48:58,864 - api_logger - INFO - Fetching latest prompt for request_type: interview_experience
2025-05-27 11:49:00,201 - api_logger - INFO - Interview experience messages created successfully.
2025-05-27 11:49:00,324 - api_logger - INFO - Generating response with model: gpt-4o-mini
2025-05-27 11:49:07,924 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 11:49:07,962 - api_logger - INFO - Response received successfully.
2025-05-27 11:49:07,964 - api_logger - INFO - Inserting record for session_id: test-session-ved, request_type: interview_experience
2025-05-27 11:49:08,415 - api_logger - INFO - Extracted questions for session_id: test-session-ved: [{'request_type': 'job_title', 'questions': ['What programming languages are you most proficient in, specifically for data analysis, and how have you used them in past projects?', 'Can you describe your experience with statistical analysis and the types of statistical tests you have employed in your work?', 'How do you approach data preprocessing and cleaning before analysis? Could you provide an example from your previous experience?', 'Which machine learning algorithms are you most familiar with, and can you walk us through a project where you implemented one of these algorithms?', 'Can you explain your experience with data visualization tools and how you use them to communicate your findings to non-technical stakeholders?', 'Describe a scenario where you utilized a specific data mining technique to solve a business problem. What was the outcome?', 'How do you ensure the accuracy and reliability of your data models? Could you share a specific example?', 'What experience do you have with big data technologies, such as Hadoop or Spark? How have you applied these in your work?', 'Could you elaborate on your understanding of A/B testing and your experience implementing it in your analyses?', 'Describe a project where you had to collaborate with a cross-functional team. How did you handle differing opinions and reach a consensus?', 'What is your experience with cloud computing platforms for data analysis, such as AWS or Azure?', "How do you stay updated with the latest trends and technologies in data science? Can you give an example of how you've implemented new knowledge in your work?", 'How do you prioritize competing tasks when working on multiple data science projects? Can you provide an example?', 'Can you describe your approach to mentoring or training team members who may be less experienced in data science skills?', 'What has been your most challenging data science project to date, and what did you learn from it?']}]
2025-05-27 11:49:08,416 - api_logger - INFO - Interview Experience Request with Session ID: test-session-ved Successfully processed
2025-05-27 11:55:44,067 - api_logger - INFO - GET /audio/voices endpoint called
2025-05-27 11:55:44,068 - api_logger - INFO - Returning voices response with 5 voices
2025-05-27 21:49:27,017 - api_logger - INFO - Audio generation request received for 2 questions
2025-05-27 21:49:27,017 - api_logger - INFO - Created audio job: audio_job_155ed25b7df9
2025-05-27 21:49:27,017 - api_logger - INFO - Audio generation job created: audio_job_155ed25b7df9
2025-05-27 21:49:27,017 - api_logger - INFO - Starting background audio generation for job: audio_job_155ed25b7df9
2025-05-27 21:49:27,017 - api_logger - INFO - Updated job audio_job_155ed25b7df9: status=processing, progress=0
2025-05-27 21:49:27,017 - api_logger - INFO - Starting audio generation for 2 questions
2025-05-27 21:49:27,017 - api_logger - INFO - Generating audio for text: what is the role of data scientist...
2025-05-27 21:49:27,755 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 21:49:27,939 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_155ed25b7df9\question_1_734912b8.mp3
2025-05-27 21:49:27,939 - api_logger - INFO - Generated audio for question 1/2
2025-05-27 21:49:27,939 - api_logger - INFO - Generating audio for text: what is your strength...
2025-05-27 21:49:28,316 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-27 21:49:28,351 - api_logger - INFO - Audio generated successfully: audio_files\audio_job_155ed25b7df9\question_2_709c03d7.mp3
2025-05-27 21:49:28,351 - api_logger - INFO - Generated audio for question 2/2
2025-05-27 21:49:28,351 - api_logger - INFO - Audio generation completed. 2/2 files generated
2025-05-27 21:49:28,351 - api_logger - INFO - Added audio file to job audio_job_155ed25b7df9: /audio/download/audio_job_155ed25b7df9/question_1_734912b8.mp3
2025-05-27 21:49:28,351 - api_logger - INFO - Added audio file to job audio_job_155ed25b7df9: /audio/download/audio_job_155ed25b7df9/question_2_709c03d7.mp3
2025-05-27 21:49:28,351 - api_logger - INFO - Updated job audio_job_155ed25b7df9: status=completed, progress=100
2025-05-27 21:49:28,351 - api_logger - INFO - Audio generation completed successfully for job: audio_job_155ed25b7df9
