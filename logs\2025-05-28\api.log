2025-05-28 06:07:28,512 - api_logger - INFO - Audio generation request received for 1 questions
2025-05-28 06:07:28,512 - api_logger - INFO - Created audio job: audio_job_74085e6da8c7
2025-05-28 06:07:28,512 - api_logger - INFO - Audio generation job created: audio_job_74085e6da8c7
2025-05-28 06:07:28,512 - api_logger - INFO - Starting background audio generation for job: audio_job_74085e6da8c7
2025-05-28 06:07:28,512 - api_logger - INFO - Updated job audio_job_74085e6da8c7: status=processing, progress=0
2025-05-28 06:07:28,512 - api_logger - INFO - Starting audio generation for 1 questions
2025-05-28 06:07:28,512 - api_logger - INFO - Generating audio for text: what is your strength?...
2025-05-28 06:07:29,152 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/zbj5pYu7PWmTR3zNpMct "HTTP/1.1 400 Bad Request"
2025-05-28 06:07:29,205 - api_logger - ERROR - Error generating audio: headers: {'date': 'Wed, 28 May 2025 13:07:31 GMT', 'server': 'uvicorn', 'content-length': '191', 'content-type': 'application/json', 'access-control-allow-origin': '*', 'access-control-allow-headers': '*', 'access-control-allow-methods': 'POST, PATCH, OPTIONS, DELETE, GET, PUT', 'access-control-max-age': '600', 'strict-transport-security': 'max-age=31536000; includeSubDomains', 'x-trace-id': '2cadb080791390450f91f67cde4ad2cf', 'via': '1.1 google, 1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'}, status_code: 400, body: {'detail': {'status': 'voice_limit_reached', 'message': 'You have reached your maximum amount of custom voices (30 / 30). You can upgrade your subscription to increase your custom voice limit.'}}
2025-05-28 06:07:29,205 - api_logger - ERROR - Failed to generate audio for question 1: headers: {'date': 'Wed, 28 May 2025 13:07:31 GMT', 'server': 'uvicorn', 'content-length': '191', 'content-type': 'application/json', 'access-control-allow-origin': '*', 'access-control-allow-headers': '*', 'access-control-allow-methods': 'POST, PATCH, OPTIONS, DELETE, GET, PUT', 'access-control-max-age': '600', 'strict-transport-security': 'max-age=31536000; includeSubDomains', 'x-trace-id': '2cadb080791390450f91f67cde4ad2cf', 'via': '1.1 google, 1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'}, status_code: 400, body: {'detail': {'status': 'voice_limit_reached', 'message': 'You have reached your maximum amount of custom voices (30 / 30). You can upgrade your subscription to increase your custom voice limit.'}}
2025-05-28 06:07:29,205 - api_logger - INFO - Audio generation completed. 0/1 files generated
2025-05-28 06:07:29,205 - api_logger - INFO - Updated job audio_job_74085e6da8c7: status=partial, progress=0
2025-05-28 06:07:29,205 - api_logger - WARNING - Partial completion for job audio_job_74085e6da8c7: 0/1 files
2025-05-28 06:26:45,586 - api_logger - INFO - S3 service initialized for bucket: my-interview-practice
2025-05-28 06:27:00,954 - api_logger - INFO - S3 bucket my-interview-practice is accessible
2025-05-28 06:27:02,018 - api_logger - INFO - S3 bucket my-interview-practice is accessible
2025-05-28 06:27:02,018 - api_logger - INFO - Generating audio for text: Hello, this is a test audio file for S3 integratio...
2025-05-28 06:27:03,114 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-28 06:27:04,076 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/test_job_123/question_0_20250528_062703.mp3
2025-05-28 06:27:04,076 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/test_job_123/question_0_20250528_062703.mp3
2025-05-28 06:27:04,649 - api_logger - INFO - Deleted 1 files for job test_job_123
2025-05-28 06:27:04,649 - api_logger - INFO - Starting audio generation for 3 questions
2025-05-28 06:27:04,650 - api_logger - INFO - Generating audio for text: What is your greatest strength?...
2025-05-28 06:27:05,152 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-28 06:27:06,840 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/test_multiple_123/question_0_20250528_062705.mp3
2025-05-28 06:27:06,844 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/test_multiple_123/question_0_20250528_062705.mp3
2025-05-28 06:27:06,844 - api_logger - INFO - Generated audio for question 1/3
2025-05-28 06:27:06,845 - api_logger - INFO - Generating audio for text: Tell me about yourself....
2025-05-28 06:27:07,324 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-28 06:27:07,983 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/test_multiple_123/question_1_20250528_062707.mp3
2025-05-28 06:27:07,983 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/test_multiple_123/question_1_20250528_062707.mp3
2025-05-28 06:27:07,983 - api_logger - INFO - Generated audio for question 2/3
2025-05-28 06:27:07,983 - api_logger - INFO - Generating audio for text: Why do you want to work here?...
2025-05-28 06:27:08,467 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-28 06:27:09,118 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/test_multiple_123/question_2_20250528_062708.mp3
2025-05-28 06:27:09,119 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/test_multiple_123/question_2_20250528_062708.mp3
2025-05-28 06:27:09,119 - api_logger - INFO - Generated audio for question 3/3
2025-05-28 06:27:09,119 - api_logger - INFO - Audio generation completed. 3/3 files generated
2025-05-28 06:27:09,808 - api_logger - INFO - Deleted 3 files for job test_multiple_123
2025-05-28 06:30:06,201 - api_logger - INFO - S3 service initialized for bucket: my-interview-practice
2025-05-28 06:30:06,211 - api_logger - INFO - S3 service initialized for bucket: my-interview-practice
2025-05-28 06:30:07,830 - api_logger - INFO - S3 bucket my-interview-practice is accessible
2025-05-28 06:30:07,916 - api_logger - INFO - S3 bucket my-interview-practice is accessible
2025-05-28 06:30:42,828 - api_logger - INFO - S3 service initialized for bucket: my-interview-practice
2025-05-28 06:30:43,710 - api_logger - INFO - S3 bucket my-interview-practice is accessible
2025-05-28 06:30:47,843 - api_logger - INFO - S3 service initialized for bucket: my-interview-practice
2025-05-28 06:30:48,660 - api_logger - INFO - S3 bucket my-interview-practice is accessible
2025-05-28 06:32:59,688 - api_logger - INFO - S3 service initialized for bucket: my-interview-practice
2025-05-28 06:33:00,591 - api_logger - INFO - S3 bucket my-interview-practice is accessible
2025-05-28 06:35:45,650 - api_logger - INFO - Audio generation request received for 2 questions
2025-05-28 06:35:45,657 - api_logger - INFO - Created audio job: audio_job_ae5418429afa
2025-05-28 06:35:45,657 - api_logger - INFO - Audio generation job created: audio_job_ae5418429afa
2025-05-28 06:35:45,663 - api_logger - INFO - Starting background audio generation for job: audio_job_ae5418429afa
2025-05-28 06:35:45,663 - api_logger - INFO - Updated job audio_job_ae5418429afa: status=processing, progress=0
2025-05-28 06:35:45,663 - api_logger - INFO - Starting audio generation for 2 questions
2025-05-28 06:35:45,665 - api_logger - INFO - Generating audio for text: what are your hobbies?...
2025-05-28 06:35:46,683 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/zbj5pYu7PWmTR3zNpMct "HTTP/1.1 400 Bad Request"
2025-05-28 06:35:46,687 - api_logger - ERROR - Error generating audio: headers: {'date': 'Wed, 28 May 2025 13:35:48 GMT', 'server': 'uvicorn', 'content-length': '191', 'content-type': 'application/json', 'access-control-allow-origin': '*', 'access-control-allow-headers': '*', 'access-control-allow-methods': 'POST, PATCH, OPTIONS, DELETE, GET, PUT', 'access-control-max-age': '600', 'strict-transport-security': 'max-age=31536000; includeSubDomains', 'x-trace-id': '8acc3dcefe8f6dc98367cf798abd5950', 'via': '1.1 google, 1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'}, status_code: 400, body: {'detail': {'status': 'voice_limit_reached', 'message': 'You have reached your maximum amount of custom voices (30 / 30). You can upgrade your subscription to increase your custom voice limit.'}}
2025-05-28 06:35:46,687 - api_logger - ERROR - Failed to generate audio for question 1: headers: {'date': 'Wed, 28 May 2025 13:35:48 GMT', 'server': 'uvicorn', 'content-length': '191', 'content-type': 'application/json', 'access-control-allow-origin': '*', 'access-control-allow-headers': '*', 'access-control-allow-methods': 'POST, PATCH, OPTIONS, DELETE, GET, PUT', 'access-control-max-age': '600', 'strict-transport-security': 'max-age=31536000; includeSubDomains', 'x-trace-id': '8acc3dcefe8f6dc98367cf798abd5950', 'via': '1.1 google, 1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'}, status_code: 400, body: {'detail': {'status': 'voice_limit_reached', 'message': 'You have reached your maximum amount of custom voices (30 / 30). You can upgrade your subscription to increase your custom voice limit.'}}
2025-05-28 06:35:46,687 - api_logger - INFO - Generating audio for text: what is your expected salary...
2025-05-28 06:35:47,206 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/zbj5pYu7PWmTR3zNpMct "HTTP/1.1 400 Bad Request"
2025-05-28 06:35:47,206 - api_logger - ERROR - Error generating audio: headers: {'date': 'Wed, 28 May 2025 13:35:49 GMT', 'server': 'uvicorn', 'content-length': '191', 'content-type': 'application/json', 'access-control-allow-origin': '*', 'access-control-allow-headers': '*', 'access-control-allow-methods': 'POST, PATCH, OPTIONS, DELETE, GET, PUT', 'access-control-max-age': '600', 'strict-transport-security': 'max-age=31536000; includeSubDomains', 'x-trace-id': '41b17a4c2b8087421ea39da8396f3c3e', 'via': '1.1 google, 1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'}, status_code: 400, body: {'detail': {'status': 'voice_limit_reached', 'message': 'You have reached your maximum amount of custom voices (30 / 30). You can upgrade your subscription to increase your custom voice limit.'}}
2025-05-28 06:35:47,206 - api_logger - ERROR - Failed to generate audio for question 2: headers: {'date': 'Wed, 28 May 2025 13:35:49 GMT', 'server': 'uvicorn', 'content-length': '191', 'content-type': 'application/json', 'access-control-allow-origin': '*', 'access-control-allow-headers': '*', 'access-control-allow-methods': 'POST, PATCH, OPTIONS, DELETE, GET, PUT', 'access-control-max-age': '600', 'strict-transport-security': 'max-age=31536000; includeSubDomains', 'x-trace-id': '41b17a4c2b8087421ea39da8396f3c3e', 'via': '1.1 google, 1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'}, status_code: 400, body: {'detail': {'status': 'voice_limit_reached', 'message': 'You have reached your maximum amount of custom voices (30 / 30). You can upgrade your subscription to increase your custom voice limit.'}}
2025-05-28 06:35:47,207 - api_logger - INFO - Audio generation completed. 0/2 files generated
2025-05-28 06:35:47,207 - api_logger - INFO - Updated job audio_job_ae5418429afa: status=partial, progress=0
2025-05-28 06:35:47,207 - api_logger - WARNING - Partial completion for job audio_job_ae5418429afa: 0/2 files
2025-05-28 06:44:10,096 - api_logger - INFO - S3 service initialized for bucket: my-interview-practice
2025-05-28 06:44:11,169 - api_logger - INFO - S3 bucket my-interview-practice is accessible
2025-05-28 06:45:12,594 - api_logger - INFO - S3 service initialized for bucket: my-interview-practice
2025-05-28 06:45:13,345 - api_logger - INFO - S3 bucket my-interview-practice is accessible
2025-05-28 06:45:26,021 - api_logger - INFO - Audio generation request received for 2 questions
2025-05-28 06:45:26,021 - api_logger - INFO - Created audio job: audio_job_0961165ea2c8
2025-05-28 06:45:26,021 - api_logger - INFO - Audio generation job created: audio_job_0961165ea2c8
2025-05-28 06:45:26,025 - api_logger - INFO - Starting background audio generation for job: audio_job_0961165ea2c8
2025-05-28 06:45:26,025 - api_logger - INFO - Updated job audio_job_0961165ea2c8: status=processing, progress=0
2025-05-28 06:45:26,025 - api_logger - INFO - Starting audio generation for 2 questions
2025-05-28 06:45:26,025 - api_logger - INFO - Generating audio for text: What are your hobbies?... using voice_id: cgSgspJ2msm6clMCkdW9, model: eleven_flash_v2_5
2025-05-28 06:45:26,753 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/cgSgspJ2msm6clMCkdW9 "HTTP/1.1 200 OK"
2025-05-28 06:45:26,826 - api_logger - INFO - Audio data generated successfully, size: 20107 bytes
2025-05-28 06:45:28,114 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_0961165ea2c8/question_0_20250528_064526.mp3
2025-05-28 06:45:28,114 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_0961165ea2c8/question_0_20250528_064526.mp3
2025-05-28 06:45:28,114 - api_logger - INFO - Generated audio for question 1/2
2025-05-28 06:45:28,114 - api_logger - INFO - Generating audio for text: Tell me about yourself... using voice_id: cgSgspJ2msm6clMCkdW9, model: eleven_flash_v2_5
2025-05-28 06:45:28,829 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/cgSgspJ2msm6clMCkdW9 "HTTP/1.1 200 OK"
2025-05-28 06:45:28,832 - api_logger - INFO - Audio data generated successfully, size: 17599 bytes
2025-05-28 06:45:30,212 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_0961165ea2c8/question_1_20250528_064528.mp3
2025-05-28 06:45:30,218 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_0961165ea2c8/question_1_20250528_064528.mp3
2025-05-28 06:45:30,218 - api_logger - INFO - Generated audio for question 2/2
2025-05-28 06:45:30,218 - api_logger - INFO - Audio generation completed. 2/2 files generated
2025-05-28 06:45:30,218 - api_logger - ERROR - Error adding audio file to job audio_job_0961165ea2c8: 'AudioFile' object has no attribute 'file_path'
2025-05-28 06:45:30,218 - api_logger - ERROR - Error adding audio file to job audio_job_0961165ea2c8: 'AudioFile' object has no attribute 'file_path'
2025-05-28 06:45:30,219 - api_logger - INFO - Updated job audio_job_0961165ea2c8: status=completed, progress=100
2025-05-28 06:45:30,219 - api_logger - INFO - Audio generation completed successfully for job: audio_job_0961165ea2c8
2025-05-28 06:45:33,664 - api_logger - INFO - S3 service initialized for bucket: my-interview-practice
2025-05-28 06:45:34,403 - api_logger - INFO - S3 bucket my-interview-practice is accessible
2025-05-28 06:45:34,418 - api_logger - INFO - Audio generation request received for 1 questions
2025-05-28 06:45:34,418 - api_logger - INFO - Created audio job: audio_job_dd73d970c461
2025-05-28 06:45:34,418 - api_logger - INFO - Audio generation job created: audio_job_dd73d970c461
2025-05-28 06:45:34,418 - api_logger - INFO - Starting background audio generation for job: audio_job_dd73d970c461
2025-05-28 06:45:34,418 - api_logger - INFO - Updated job audio_job_dd73d970c461: status=processing, progress=0
2025-05-28 06:45:34,418 - api_logger - INFO - Starting audio generation for 1 questions
2025-05-28 06:45:34,418 - api_logger - INFO - Generating audio for text: Hello, this is a test message.... using voice_id: 21m00Tcm4TlvDq8ikWAM, model: eleven_flash_v2_5
2025-05-28 06:45:34,862 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-28 06:45:34,880 - api_logger - INFO - Audio data generated successfully, size: 25958 bytes
2025-05-28 06:45:35,590 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_dd73d970c461/question_0_20250528_064534.mp3
2025-05-28 06:45:35,593 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_dd73d970c461/question_0_20250528_064534.mp3
2025-05-28 06:45:35,593 - api_logger - INFO - Generated audio for question 1/1
2025-05-28 06:45:35,593 - api_logger - INFO - Audio generation completed. 1/1 files generated
2025-05-28 06:45:35,593 - api_logger - ERROR - Error adding audio file to job audio_job_dd73d970c461: 'AudioFile' object has no attribute 'file_path'
2025-05-28 06:45:35,593 - api_logger - INFO - Updated job audio_job_dd73d970c461: status=completed, progress=100
2025-05-28 06:45:35,593 - api_logger - INFO - Audio generation completed successfully for job: audio_job_dd73d970c461
2025-05-28 06:45:36,464 - api_logger - INFO - Audio generation request received for 1 questions
2025-05-28 06:45:36,464 - api_logger - INFO - Created audio job: audio_job_488f34edc7da
2025-05-28 06:45:36,464 - api_logger - INFO - Audio generation job created: audio_job_488f34edc7da
2025-05-28 06:45:36,464 - api_logger - INFO - Starting background audio generation for job: audio_job_488f34edc7da
2025-05-28 06:45:36,464 - api_logger - INFO - Updated job audio_job_488f34edc7da: status=processing, progress=0
2025-05-28 06:45:36,464 - api_logger - INFO - Starting audio generation for 1 questions
2025-05-28 06:45:36,464 - api_logger - INFO - Generating audio for text: Hello, this is a test message.... using voice_id: cgSgspJ2msm6clMCkdW9, model: eleven_flash_v2_5
2025-05-28 06:45:36,908 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/cgSgspJ2msm6clMCkdW9 "HTTP/1.1 200 OK"
2025-05-28 06:45:36,926 - api_logger - INFO - Audio data generated successfully, size: 28884 bytes
2025-05-28 06:45:37,424 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_488f34edc7da/question_0_20250528_064536.mp3
2025-05-28 06:45:37,426 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_488f34edc7da/question_0_20250528_064536.mp3
2025-05-28 06:45:37,426 - api_logger - INFO - Generated audio for question 1/1
2025-05-28 06:45:37,426 - api_logger - INFO - Audio generation completed. 1/1 files generated
2025-05-28 06:45:37,426 - api_logger - ERROR - Error adding audio file to job audio_job_488f34edc7da: 'AudioFile' object has no attribute 'file_path'
2025-05-28 06:45:37,426 - api_logger - INFO - Updated job audio_job_488f34edc7da: status=completed, progress=100
2025-05-28 06:45:37,426 - api_logger - INFO - Audio generation completed successfully for job: audio_job_488f34edc7da
2025-05-28 06:45:38,486 - api_logger - INFO - Audio generation request received for 1 questions
2025-05-28 06:45:38,486 - api_logger - INFO - Created audio job: audio_job_176a1925fa07
2025-05-28 06:45:38,486 - api_logger - INFO - Audio generation job created: audio_job_176a1925fa07
2025-05-28 06:45:38,486 - api_logger - INFO - Starting background audio generation for job: audio_job_176a1925fa07
2025-05-28 06:45:38,486 - api_logger - INFO - Updated job audio_job_176a1925fa07: status=processing, progress=0
2025-05-28 06:45:38,486 - api_logger - INFO - Starting audio generation for 1 questions
2025-05-28 06:45:38,486 - api_logger - INFO - Generating audio for text: Hello, this is a test message.... using voice_id: nPczCjzI2devNBz1zQrb, model: eleven_flash_v2_5
2025-05-28 06:45:38,897 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/nPczCjzI2devNBz1zQrb "HTTP/1.1 200 OK"
2025-05-28 06:45:38,917 - api_logger - INFO - Audio data generated successfully, size: 27212 bytes
2025-05-28 06:45:39,388 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_176a1925fa07/question_0_20250528_064538.mp3
2025-05-28 06:45:39,389 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_176a1925fa07/question_0_20250528_064538.mp3
2025-05-28 06:45:39,389 - api_logger - INFO - Generated audio for question 1/1
2025-05-28 06:45:39,389 - api_logger - INFO - Audio generation completed. 1/1 files generated
2025-05-28 06:45:39,389 - api_logger - ERROR - Error adding audio file to job audio_job_176a1925fa07: 'AudioFile' object has no attribute 'file_path'
2025-05-28 06:45:39,389 - api_logger - INFO - Updated job audio_job_176a1925fa07: status=completed, progress=100
2025-05-28 06:45:39,389 - api_logger - INFO - Audio generation completed successfully for job: audio_job_176a1925fa07
2025-05-28 06:45:40,514 - api_logger - INFO - Audio generation request received for 1 questions
2025-05-28 06:45:40,514 - api_logger - INFO - Created audio job: audio_job_a1f30bad0f91
2025-05-28 06:45:40,514 - api_logger - INFO - Audio generation job created: audio_job_a1f30bad0f91
2025-05-28 06:45:40,514 - api_logger - INFO - Starting background audio generation for job: audio_job_a1f30bad0f91
2025-05-28 06:45:40,514 - api_logger - INFO - Updated job audio_job_a1f30bad0f91: status=processing, progress=0
2025-05-28 06:45:40,514 - api_logger - INFO - Starting audio generation for 1 questions
2025-05-28 06:45:40,514 - api_logger - INFO - Generating audio for text: Hello, this is a test message.... using voice_id: TX3LPaxmHKxFdv7VOQHJ, model: eleven_flash_v2_5
2025-05-28 06:45:40,922 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/TX3LPaxmHKxFdv7VOQHJ "HTTP/1.1 200 OK"
2025-05-28 06:45:40,934 - api_logger - INFO - Audio data generated successfully, size: 29720 bytes
2025-05-28 06:45:41,414 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_a1f30bad0f91/question_0_20250528_064540.mp3
2025-05-28 06:45:41,416 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_a1f30bad0f91/question_0_20250528_064540.mp3
2025-05-28 06:45:41,416 - api_logger - INFO - Generated audio for question 1/1
2025-05-28 06:45:41,416 - api_logger - INFO - Audio generation completed. 1/1 files generated
2025-05-28 06:45:41,416 - api_logger - ERROR - Error adding audio file to job audio_job_a1f30bad0f91: 'AudioFile' object has no attribute 'file_path'
2025-05-28 06:45:41,416 - api_logger - INFO - Updated job audio_job_a1f30bad0f91: status=completed, progress=100
2025-05-28 06:45:41,416 - api_logger - INFO - Audio generation completed successfully for job: audio_job_a1f30bad0f91
2025-05-28 06:45:42,556 - api_logger - INFO - Audio generation request received for 1 questions
2025-05-28 06:45:42,556 - api_logger - INFO - Created audio job: audio_job_3316fef042ba
2025-05-28 06:45:42,556 - api_logger - INFO - Audio generation job created: audio_job_3316fef042ba
2025-05-28 06:45:42,556 - api_logger - INFO - Starting background audio generation for job: audio_job_3316fef042ba
2025-05-28 06:45:42,556 - api_logger - INFO - Updated job audio_job_3316fef042ba: status=processing, progress=0
2025-05-28 06:45:42,556 - api_logger - INFO - Starting audio generation for 1 questions
2025-05-28 06:45:42,556 - api_logger - INFO - Generating audio for text: Hello, this is a test message.... using voice_id: JBFqnCBsd6RMkjVDRZzb, model: eleven_flash_v2_5
2025-05-28 06:45:42,964 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/JBFqnCBsd6RMkjVDRZzb "HTTP/1.1 200 OK"
2025-05-28 06:45:42,985 - api_logger - INFO - Audio data generated successfully, size: 30556 bytes
2025-05-28 06:45:43,453 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_3316fef042ba/question_0_20250528_064542.mp3
2025-05-28 06:45:43,460 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_3316fef042ba/question_0_20250528_064542.mp3
2025-05-28 06:45:43,460 - api_logger - INFO - Generated audio for question 1/1
2025-05-28 06:45:43,460 - api_logger - INFO - Audio generation completed. 1/1 files generated
2025-05-28 06:45:43,460 - api_logger - ERROR - Error adding audio file to job audio_job_3316fef042ba: 'AudioFile' object has no attribute 'file_path'
2025-05-28 06:45:43,460 - api_logger - INFO - Updated job audio_job_3316fef042ba: status=completed, progress=100
2025-05-28 06:45:43,460 - api_logger - INFO - Audio generation completed successfully for job: audio_job_3316fef042ba
2025-05-28 06:46:39,783 - api_logger - INFO - Audio generation request received for 2 questions
2025-05-28 06:46:39,783 - api_logger - INFO - Created audio job: audio_job_8e912a3f6de8
2025-05-28 06:46:39,783 - api_logger - INFO - Audio generation job created: audio_job_8e912a3f6de8
2025-05-28 06:46:39,785 - api_logger - INFO - Starting background audio generation for job: audio_job_8e912a3f6de8
2025-05-28 06:46:39,785 - api_logger - INFO - Updated job audio_job_8e912a3f6de8: status=processing, progress=0
2025-05-28 06:46:39,785 - api_logger - INFO - Starting audio generation for 2 questions
2025-05-28 06:46:39,785 - api_logger - INFO - Generating audio for text: What are your hobbies?... using voice_id: cgSgspJ2msm6clMCkdW9, model: eleven_flash_v2_5
2025-05-28 06:46:40,731 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/cgSgspJ2msm6clMCkdW9 "HTTP/1.1 200 OK"
2025-05-28 06:46:40,746 - api_logger - INFO - Audio data generated successfully, size: 20107 bytes
2025-05-28 06:46:42,019 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_8e912a3f6de8/question_0_20250528_064640.mp3
2025-05-28 06:46:42,019 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_8e912a3f6de8/question_0_20250528_064640.mp3
2025-05-28 06:46:42,019 - api_logger - INFO - Generated audio for question 1/2
2025-05-28 06:46:42,019 - api_logger - INFO - Generating audio for text: Tell me about yourself... using voice_id: cgSgspJ2msm6clMCkdW9, model: eleven_flash_v2_5
2025-05-28 06:46:42,407 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/cgSgspJ2msm6clMCkdW9 "HTTP/1.1 200 OK"
2025-05-28 06:46:42,416 - api_logger - INFO - Audio data generated successfully, size: 17181 bytes
2025-05-28 06:46:42,913 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_8e912a3f6de8/question_1_20250528_064642.mp3
2025-05-28 06:46:42,920 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_8e912a3f6de8/question_1_20250528_064642.mp3
2025-05-28 06:46:42,921 - api_logger - INFO - Generated audio for question 2/2
2025-05-28 06:46:42,921 - api_logger - INFO - Audio generation completed. 2/2 files generated
2025-05-28 06:46:42,921 - api_logger - ERROR - Error adding audio file to job audio_job_8e912a3f6de8: 'AudioFile' object has no attribute 'file_path'
2025-05-28 06:46:42,921 - api_logger - ERROR - Error adding audio file to job audio_job_8e912a3f6de8: 'AudioFile' object has no attribute 'file_path'
2025-05-28 06:46:42,921 - api_logger - INFO - Updated job audio_job_8e912a3f6de8: status=completed, progress=100
2025-05-28 06:46:42,921 - api_logger - INFO - Audio generation completed successfully for job: audio_job_8e912a3f6de8
2025-05-28 06:46:46,136 - api_logger - INFO - S3 service initialized for bucket: my-interview-practice
2025-05-28 06:46:46,900 - api_logger - INFO - S3 bucket my-interview-practice is accessible
2025-05-28 06:46:46,922 - api_logger - INFO - Audio generation request received for 1 questions
2025-05-28 06:46:46,922 - api_logger - INFO - Created audio job: audio_job_955624963004
2025-05-28 06:46:46,922 - api_logger - INFO - Audio generation job created: audio_job_955624963004
2025-05-28 06:46:46,922 - api_logger - INFO - Starting background audio generation for job: audio_job_955624963004
2025-05-28 06:46:46,922 - api_logger - INFO - Updated job audio_job_955624963004: status=processing, progress=0
2025-05-28 06:46:46,922 - api_logger - INFO - Starting audio generation for 1 questions
2025-05-28 06:46:46,922 - api_logger - INFO - Generating audio for text: Hello, this is a test message.... using voice_id: 21m00Tcm4TlvDq8ikWAM, model: eleven_flash_v2_5
2025-05-28 06:46:47,408 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-28 06:46:47,430 - api_logger - INFO - Audio data generated successfully, size: 25122 bytes
2025-05-28 06:46:48,330 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_955624963004/question_0_20250528_064647.mp3
2025-05-28 06:46:48,337 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_955624963004/question_0_20250528_064647.mp3
2025-05-28 06:46:48,337 - api_logger - INFO - Generated audio for question 1/1
2025-05-28 06:46:48,337 - api_logger - INFO - Audio generation completed. 1/1 files generated
2025-05-28 06:46:48,337 - api_logger - INFO - Added audio file to job audio_job_955624963004: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_955624963004/question_0_20250528_064647.mp3
2025-05-28 06:46:48,337 - api_logger - INFO - Updated job audio_job_955624963004: status=completed, progress=100
2025-05-28 06:46:48,337 - api_logger - INFO - Audio generation completed successfully for job: audio_job_955624963004
2025-05-28 06:46:48,950 - api_logger - INFO - Audio generation request received for 1 questions
2025-05-28 06:46:48,950 - api_logger - INFO - Created audio job: audio_job_1f3cda02ae1e
2025-05-28 06:46:48,950 - api_logger - INFO - Audio generation job created: audio_job_1f3cda02ae1e
2025-05-28 06:46:48,950 - api_logger - INFO - Starting background audio generation for job: audio_job_1f3cda02ae1e
2025-05-28 06:46:48,950 - api_logger - INFO - Updated job audio_job_1f3cda02ae1e: status=processing, progress=0
2025-05-28 06:46:48,950 - api_logger - INFO - Starting audio generation for 1 questions
2025-05-28 06:46:48,951 - api_logger - INFO - Generating audio for text: Hello, this is a test message.... using voice_id: cgSgspJ2msm6clMCkdW9, model: eleven_flash_v2_5
2025-05-28 06:46:49,463 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/cgSgspJ2msm6clMCkdW9 "HTTP/1.1 200 OK"
2025-05-28 06:46:49,484 - api_logger - INFO - Audio data generated successfully, size: 28884 bytes
2025-05-28 06:46:49,977 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_1f3cda02ae1e/question_0_20250528_064649.mp3
2025-05-28 06:46:49,979 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_1f3cda02ae1e/question_0_20250528_064649.mp3
2025-05-28 06:46:49,979 - api_logger - INFO - Generated audio for question 1/1
2025-05-28 06:46:49,979 - api_logger - INFO - Audio generation completed. 1/1 files generated
2025-05-28 06:46:49,979 - api_logger - INFO - Added audio file to job audio_job_1f3cda02ae1e: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_1f3cda02ae1e/question_0_20250528_064649.mp3
2025-05-28 06:46:49,979 - api_logger - INFO - Updated job audio_job_1f3cda02ae1e: status=completed, progress=100
2025-05-28 06:46:49,979 - api_logger - INFO - Audio generation completed successfully for job: audio_job_1f3cda02ae1e
2025-05-28 06:46:50,963 - api_logger - INFO - Audio generation request received for 1 questions
2025-05-28 06:46:50,963 - api_logger - INFO - Created audio job: audio_job_0896ffcee86b
2025-05-28 06:46:50,963 - api_logger - INFO - Audio generation job created: audio_job_0896ffcee86b
2025-05-28 06:46:50,963 - api_logger - INFO - Starting background audio generation for job: audio_job_0896ffcee86b
2025-05-28 06:46:50,963 - api_logger - INFO - Updated job audio_job_0896ffcee86b: status=processing, progress=0
2025-05-28 06:46:50,963 - api_logger - INFO - Starting audio generation for 1 questions
2025-05-28 06:46:50,963 - api_logger - INFO - Generating audio for text: Hello, this is a test message.... using voice_id: nPczCjzI2devNBz1zQrb, model: eleven_flash_v2_5
2025-05-28 06:46:51,407 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/nPczCjzI2devNBz1zQrb "HTTP/1.1 200 OK"
2025-05-28 06:46:51,427 - api_logger - INFO - Audio data generated successfully, size: 26794 bytes
2025-05-28 06:46:51,922 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_0896ffcee86b/question_0_20250528_064651.mp3
2025-05-28 06:46:51,922 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_0896ffcee86b/question_0_20250528_064651.mp3
2025-05-28 06:46:51,927 - api_logger - INFO - Generated audio for question 1/1
2025-05-28 06:46:51,927 - api_logger - INFO - Audio generation completed. 1/1 files generated
2025-05-28 06:46:51,927 - api_logger - INFO - Added audio file to job audio_job_0896ffcee86b: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_0896ffcee86b/question_0_20250528_064651.mp3
2025-05-28 06:46:51,927 - api_logger - INFO - Updated job audio_job_0896ffcee86b: status=completed, progress=100
2025-05-28 06:46:51,927 - api_logger - INFO - Audio generation completed successfully for job: audio_job_0896ffcee86b
2025-05-28 06:46:52,983 - api_logger - INFO - Audio generation request received for 1 questions
2025-05-28 06:46:52,983 - api_logger - INFO - Created audio job: audio_job_3e3c32050667
2025-05-28 06:46:52,983 - api_logger - INFO - Audio generation job created: audio_job_3e3c32050667
2025-05-28 06:46:52,983 - api_logger - INFO - Starting background audio generation for job: audio_job_3e3c32050667
2025-05-28 06:46:52,983 - api_logger - INFO - Updated job audio_job_3e3c32050667: status=processing, progress=0
2025-05-28 06:46:52,983 - api_logger - INFO - Starting audio generation for 1 questions
2025-05-28 06:46:52,983 - api_logger - INFO - Generating audio for text: Hello, this is a test message.... using voice_id: TX3LPaxmHKxFdv7VOQHJ, model: eleven_flash_v2_5
2025-05-28 06:46:53,386 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/TX3LPaxmHKxFdv7VOQHJ "HTTP/1.1 200 OK"
2025-05-28 06:46:53,400 - api_logger - INFO - Audio data generated successfully, size: 28048 bytes
2025-05-28 06:46:53,908 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_3e3c32050667/question_0_20250528_064653.mp3
2025-05-28 06:46:53,908 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_3e3c32050667/question_0_20250528_064653.mp3
2025-05-28 06:46:53,908 - api_logger - INFO - Generated audio for question 1/1
2025-05-28 06:46:53,908 - api_logger - INFO - Audio generation completed. 1/1 files generated
2025-05-28 06:46:53,908 - api_logger - INFO - Added audio file to job audio_job_3e3c32050667: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_3e3c32050667/question_0_20250528_064653.mp3
2025-05-28 06:46:53,913 - api_logger - INFO - Updated job audio_job_3e3c32050667: status=completed, progress=100
2025-05-28 06:46:53,913 - api_logger - INFO - Audio generation completed successfully for job: audio_job_3e3c32050667
2025-05-28 06:46:55,012 - api_logger - INFO - Audio generation request received for 1 questions
2025-05-28 06:46:55,012 - api_logger - INFO - Created audio job: audio_job_80514ca2d2db
2025-05-28 06:46:55,012 - api_logger - INFO - Audio generation job created: audio_job_80514ca2d2db
2025-05-28 06:46:55,012 - api_logger - INFO - Starting background audio generation for job: audio_job_80514ca2d2db
2025-05-28 06:46:55,012 - api_logger - INFO - Updated job audio_job_80514ca2d2db: status=processing, progress=0
2025-05-28 06:46:55,012 - api_logger - INFO - Starting audio generation for 1 questions
2025-05-28 06:46:55,014 - api_logger - INFO - Generating audio for text: Hello, this is a test message.... using voice_id: JBFqnCBsd6RMkjVDRZzb, model: eleven_flash_v2_5
2025-05-28 06:46:55,450 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/JBFqnCBsd6RMkjVDRZzb "HTTP/1.1 200 OK"
2025-05-28 06:46:55,470 - api_logger - INFO - Audio data generated successfully, size: 29720 bytes
2025-05-28 06:46:55,976 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_80514ca2d2db/question_0_20250528_064655.mp3
2025-05-28 06:46:55,976 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_80514ca2d2db/question_0_20250528_064655.mp3
2025-05-28 06:46:55,976 - api_logger - INFO - Generated audio for question 1/1
2025-05-28 06:46:55,976 - api_logger - INFO - Audio generation completed. 1/1 files generated
2025-05-28 06:46:55,976 - api_logger - INFO - Added audio file to job audio_job_80514ca2d2db: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_80514ca2d2db/question_0_20250528_064655.mp3
2025-05-28 06:46:55,976 - api_logger - INFO - Updated job audio_job_80514ca2d2db: status=completed, progress=100
2025-05-28 06:46:55,976 - api_logger - INFO - Audio generation completed successfully for job: audio_job_80514ca2d2db
2025-05-28 06:48:00,957 - api_logger - INFO - Audio generation request received for 2 questions
2025-05-28 06:48:00,957 - api_logger - INFO - Created audio job: audio_job_ec34bd322b44
2025-05-28 06:48:00,957 - api_logger - INFO - Audio generation job created: audio_job_ec34bd322b44
2025-05-28 06:48:00,957 - api_logger - INFO - Starting background audio generation for job: audio_job_ec34bd322b44
2025-05-28 06:48:00,957 - api_logger - INFO - Updated job audio_job_ec34bd322b44: status=processing, progress=0
2025-05-28 06:48:00,957 - api_logger - INFO - Starting audio generation for 2 questions
2025-05-28 06:48:00,957 - api_logger - INFO - Generating audio for text: What is your greatest strength?... using voice_id: 21m00Tcm4TlvDq8ikWAM, model: eleven_flash_v2_5
2025-05-28 06:48:01,479 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-28 06:48:01,496 - api_logger - INFO - Audio data generated successfully, size: 25958 bytes
2025-05-28 06:48:02,698 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_ec34bd322b44/question_0_20250528_064801.mp3
2025-05-28 06:48:02,698 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_ec34bd322b44/question_0_20250528_064801.mp3
2025-05-28 06:48:02,698 - api_logger - INFO - Generated audio for question 1/2
2025-05-28 06:48:02,698 - api_logger - INFO - Generating audio for text: Tell me about a challenging project you worked on.... using voice_id: 21m00Tcm4TlvDq8ikWAM, model: eleven_flash_v2_5
2025-05-28 06:48:03,172 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-28 06:48:03,189 - api_logger - INFO - Audio data generated successfully, size: 37243 bytes
2025-05-28 06:48:03,680 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_ec34bd322b44/question_1_20250528_064803.mp3
2025-05-28 06:48:03,680 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_ec34bd322b44/question_1_20250528_064803.mp3
2025-05-28 06:48:03,680 - api_logger - INFO - Generated audio for question 2/2
2025-05-28 06:48:03,680 - api_logger - INFO - Audio generation completed. 2/2 files generated
2025-05-28 06:48:03,680 - api_logger - INFO - Added audio file to job audio_job_ec34bd322b44: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_ec34bd322b44/question_0_20250528_064801.mp3
2025-05-28 06:48:03,684 - api_logger - INFO - Added audio file to job audio_job_ec34bd322b44: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_ec34bd322b44/question_1_20250528_064803.mp3
2025-05-28 06:48:03,684 - api_logger - INFO - Updated job audio_job_ec34bd322b44: status=completed, progress=100
2025-05-28 06:48:03,684 - api_logger - INFO - Audio generation completed successfully for job: audio_job_ec34bd322b44
2025-05-28 06:48:07,705 - api_logger - INFO - Audio generation request received for 2 questions
2025-05-28 06:48:07,705 - api_logger - INFO - Created audio job: audio_job_134c717220ff
2025-05-28 06:48:07,705 - api_logger - INFO - Audio generation job created: audio_job_134c717220ff
2025-05-28 06:48:07,705 - api_logger - INFO - Starting background audio generation for job: audio_job_134c717220ff
2025-05-28 06:48:07,705 - api_logger - INFO - Updated job audio_job_134c717220ff: status=processing, progress=0
2025-05-28 06:48:07,705 - api_logger - INFO - Starting audio generation for 2 questions
2025-05-28 06:48:07,705 - api_logger - INFO - Generating audio for text: what are your hobbies?... using voice_id: cgSgspJ2msm6clMCkdW9, model: eleven_flash_v2_5
2025-05-28 06:48:08,101 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/cgSgspJ2msm6clMCkdW9 "HTTP/1.1 200 OK"
2025-05-28 06:48:08,108 - api_logger - INFO - Audio data generated successfully, size: 19271 bytes
2025-05-28 06:48:08,595 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_134c717220ff/question_0_20250528_064808.mp3
2025-05-28 06:48:08,595 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_134c717220ff/question_0_20250528_064808.mp3
2025-05-28 06:48:08,595 - api_logger - INFO - Generated audio for question 1/2
2025-05-28 06:48:08,595 - api_logger - INFO - Generating audio for text: tell me about yourself... using voice_id: cgSgspJ2msm6clMCkdW9, model: eleven_flash_v2_5
2025-05-28 06:48:09,063 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/cgSgspJ2msm6clMCkdW9 "HTTP/1.1 200 OK"
2025-05-28 06:48:09,066 - api_logger - INFO - Audio data generated successfully, size: 17599 bytes
2025-05-28 06:48:09,580 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_134c717220ff/question_1_20250528_064809.mp3
2025-05-28 06:48:09,580 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_134c717220ff/question_1_20250528_064809.mp3
2025-05-28 06:48:09,580 - api_logger - INFO - Generated audio for question 2/2
2025-05-28 06:48:09,580 - api_logger - INFO - Audio generation completed. 2/2 files generated
2025-05-28 06:48:09,580 - api_logger - INFO - Added audio file to job audio_job_134c717220ff: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_134c717220ff/question_0_20250528_064808.mp3
2025-05-28 06:48:09,580 - api_logger - INFO - Added audio file to job audio_job_134c717220ff: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_134c717220ff/question_1_20250528_064809.mp3
2025-05-28 06:48:09,582 - api_logger - INFO - Updated job audio_job_134c717220ff: status=completed, progress=100
2025-05-28 06:48:09,582 - api_logger - INFO - Audio generation completed successfully for job: audio_job_134c717220ff
