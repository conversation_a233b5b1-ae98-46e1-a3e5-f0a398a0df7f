2025-05-28 06:07:28,512 - api_logger - INFO - Audio generation request received for 1 questions
2025-05-28 06:07:28,512 - api_logger - INFO - Created audio job: audio_job_74085e6da8c7
2025-05-28 06:07:28,512 - api_logger - INFO - Audio generation job created: audio_job_74085e6da8c7
2025-05-28 06:07:28,512 - api_logger - INFO - Starting background audio generation for job: audio_job_74085e6da8c7
2025-05-28 06:07:28,512 - api_logger - INFO - Updated job audio_job_74085e6da8c7: status=processing, progress=0
2025-05-28 06:07:28,512 - api_logger - INFO - Starting audio generation for 1 questions
2025-05-28 06:07:28,512 - api_logger - INFO - Generating audio for text: what is your strength?...
2025-05-28 06:07:29,152 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/zbj5pYu7PWmTR3zNpMct "HTTP/1.1 400 Bad Request"
2025-05-28 06:07:29,205 - api_logger - ERROR - Error generating audio: headers: {'date': 'Wed, 28 May 2025 13:07:31 GMT', 'server': 'uvicorn', 'content-length': '191', 'content-type': 'application/json', 'access-control-allow-origin': '*', 'access-control-allow-headers': '*', 'access-control-allow-methods': 'POST, PATCH, OPTIONS, DELETE, GET, PUT', 'access-control-max-age': '600', 'strict-transport-security': 'max-age=31536000; includeSubDomains', 'x-trace-id': '2cadb080791390450f91f67cde4ad2cf', 'via': '1.1 google, 1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'}, status_code: 400, body: {'detail': {'status': 'voice_limit_reached', 'message': 'You have reached your maximum amount of custom voices (30 / 30). You can upgrade your subscription to increase your custom voice limit.'}}
2025-05-28 06:07:29,205 - api_logger - ERROR - Failed to generate audio for question 1: headers: {'date': 'Wed, 28 May 2025 13:07:31 GMT', 'server': 'uvicorn', 'content-length': '191', 'content-type': 'application/json', 'access-control-allow-origin': '*', 'access-control-allow-headers': '*', 'access-control-allow-methods': 'POST, PATCH, OPTIONS, DELETE, GET, PUT', 'access-control-max-age': '600', 'strict-transport-security': 'max-age=31536000; includeSubDomains', 'x-trace-id': '2cadb080791390450f91f67cde4ad2cf', 'via': '1.1 google, 1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'}, status_code: 400, body: {'detail': {'status': 'voice_limit_reached', 'message': 'You have reached your maximum amount of custom voices (30 / 30). You can upgrade your subscription to increase your custom voice limit.'}}
2025-05-28 06:07:29,205 - api_logger - INFO - Audio generation completed. 0/1 files generated
2025-05-28 06:07:29,205 - api_logger - INFO - Updated job audio_job_74085e6da8c7: status=partial, progress=0
2025-05-28 06:07:29,205 - api_logger - WARNING - Partial completion for job audio_job_74085e6da8c7: 0/1 files
2025-05-28 06:26:45,586 - api_logger - INFO - S3 service initialized for bucket: my-interview-practice
2025-05-28 06:27:00,954 - api_logger - INFO - S3 bucket my-interview-practice is accessible
2025-05-28 06:27:02,018 - api_logger - INFO - S3 bucket my-interview-practice is accessible
2025-05-28 06:27:02,018 - api_logger - INFO - Generating audio for text: Hello, this is a test audio file for S3 integratio...
2025-05-28 06:27:03,114 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-28 06:27:04,076 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/test_job_123/question_0_20250528_062703.mp3
2025-05-28 06:27:04,076 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/test_job_123/question_0_20250528_062703.mp3
2025-05-28 06:27:04,649 - api_logger - INFO - Deleted 1 files for job test_job_123
2025-05-28 06:27:04,649 - api_logger - INFO - Starting audio generation for 3 questions
2025-05-28 06:27:04,650 - api_logger - INFO - Generating audio for text: What is your greatest strength?...
2025-05-28 06:27:05,152 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-28 06:27:06,840 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/test_multiple_123/question_0_20250528_062705.mp3
2025-05-28 06:27:06,844 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/test_multiple_123/question_0_20250528_062705.mp3
2025-05-28 06:27:06,844 - api_logger - INFO - Generated audio for question 1/3
2025-05-28 06:27:06,845 - api_logger - INFO - Generating audio for text: Tell me about yourself....
2025-05-28 06:27:07,324 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-28 06:27:07,983 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/test_multiple_123/question_1_20250528_062707.mp3
2025-05-28 06:27:07,983 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/test_multiple_123/question_1_20250528_062707.mp3
2025-05-28 06:27:07,983 - api_logger - INFO - Generated audio for question 2/3
2025-05-28 06:27:07,983 - api_logger - INFO - Generating audio for text: Why do you want to work here?...
2025-05-28 06:27:08,467 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM "HTTP/1.1 200 OK"
2025-05-28 06:27:09,118 - api_logger - INFO - Audio file uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/test_multiple_123/question_2_20250528_062708.mp3
2025-05-28 06:27:09,119 - api_logger - INFO - Audio generated and uploaded successfully: https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/test_multiple_123/question_2_20250528_062708.mp3
2025-05-28 06:27:09,119 - api_logger - INFO - Generated audio for question 3/3
2025-05-28 06:27:09,119 - api_logger - INFO - Audio generation completed. 3/3 files generated
2025-05-28 06:27:09,808 - api_logger - INFO - Deleted 3 files for job test_multiple_123
2025-05-28 06:30:06,201 - api_logger - INFO - S3 service initialized for bucket: my-interview-practice
2025-05-28 06:30:06,211 - api_logger - INFO - S3 service initialized for bucket: my-interview-practice
2025-05-28 06:30:07,830 - api_logger - INFO - S3 bucket my-interview-practice is accessible
2025-05-28 06:30:07,916 - api_logger - INFO - S3 bucket my-interview-practice is accessible
2025-05-28 06:30:42,828 - api_logger - INFO - S3 service initialized for bucket: my-interview-practice
2025-05-28 06:30:43,710 - api_logger - INFO - S3 bucket my-interview-practice is accessible
2025-05-28 06:30:47,843 - api_logger - INFO - S3 service initialized for bucket: my-interview-practice
2025-05-28 06:30:48,660 - api_logger - INFO - S3 bucket my-interview-practice is accessible
2025-05-28 06:32:59,688 - api_logger - INFO - S3 service initialized for bucket: my-interview-practice
2025-05-28 06:33:00,591 - api_logger - INFO - S3 bucket my-interview-practice is accessible
2025-05-28 06:35:45,650 - api_logger - INFO - Audio generation request received for 2 questions
2025-05-28 06:35:45,657 - api_logger - INFO - Created audio job: audio_job_ae5418429afa
2025-05-28 06:35:45,657 - api_logger - INFO - Audio generation job created: audio_job_ae5418429afa
2025-05-28 06:35:45,663 - api_logger - INFO - Starting background audio generation for job: audio_job_ae5418429afa
2025-05-28 06:35:45,663 - api_logger - INFO - Updated job audio_job_ae5418429afa: status=processing, progress=0
2025-05-28 06:35:45,663 - api_logger - INFO - Starting audio generation for 2 questions
2025-05-28 06:35:45,665 - api_logger - INFO - Generating audio for text: what are your hobbies?...
2025-05-28 06:35:46,683 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/zbj5pYu7PWmTR3zNpMct "HTTP/1.1 400 Bad Request"
2025-05-28 06:35:46,687 - api_logger - ERROR - Error generating audio: headers: {'date': 'Wed, 28 May 2025 13:35:48 GMT', 'server': 'uvicorn', 'content-length': '191', 'content-type': 'application/json', 'access-control-allow-origin': '*', 'access-control-allow-headers': '*', 'access-control-allow-methods': 'POST, PATCH, OPTIONS, DELETE, GET, PUT', 'access-control-max-age': '600', 'strict-transport-security': 'max-age=31536000; includeSubDomains', 'x-trace-id': '8acc3dcefe8f6dc98367cf798abd5950', 'via': '1.1 google, 1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'}, status_code: 400, body: {'detail': {'status': 'voice_limit_reached', 'message': 'You have reached your maximum amount of custom voices (30 / 30). You can upgrade your subscription to increase your custom voice limit.'}}
2025-05-28 06:35:46,687 - api_logger - ERROR - Failed to generate audio for question 1: headers: {'date': 'Wed, 28 May 2025 13:35:48 GMT', 'server': 'uvicorn', 'content-length': '191', 'content-type': 'application/json', 'access-control-allow-origin': '*', 'access-control-allow-headers': '*', 'access-control-allow-methods': 'POST, PATCH, OPTIONS, DELETE, GET, PUT', 'access-control-max-age': '600', 'strict-transport-security': 'max-age=31536000; includeSubDomains', 'x-trace-id': '8acc3dcefe8f6dc98367cf798abd5950', 'via': '1.1 google, 1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'}, status_code: 400, body: {'detail': {'status': 'voice_limit_reached', 'message': 'You have reached your maximum amount of custom voices (30 / 30). You can upgrade your subscription to increase your custom voice limit.'}}
2025-05-28 06:35:46,687 - api_logger - INFO - Generating audio for text: what is your expected salary...
2025-05-28 06:35:47,206 - httpx - INFO - HTTP Request: POST https://api.elevenlabs.io/v1/text-to-speech/zbj5pYu7PWmTR3zNpMct "HTTP/1.1 400 Bad Request"
2025-05-28 06:35:47,206 - api_logger - ERROR - Error generating audio: headers: {'date': 'Wed, 28 May 2025 13:35:49 GMT', 'server': 'uvicorn', 'content-length': '191', 'content-type': 'application/json', 'access-control-allow-origin': '*', 'access-control-allow-headers': '*', 'access-control-allow-methods': 'POST, PATCH, OPTIONS, DELETE, GET, PUT', 'access-control-max-age': '600', 'strict-transport-security': 'max-age=31536000; includeSubDomains', 'x-trace-id': '41b17a4c2b8087421ea39da8396f3c3e', 'via': '1.1 google, 1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'}, status_code: 400, body: {'detail': {'status': 'voice_limit_reached', 'message': 'You have reached your maximum amount of custom voices (30 / 30). You can upgrade your subscription to increase your custom voice limit.'}}
2025-05-28 06:35:47,206 - api_logger - ERROR - Failed to generate audio for question 2: headers: {'date': 'Wed, 28 May 2025 13:35:49 GMT', 'server': 'uvicorn', 'content-length': '191', 'content-type': 'application/json', 'access-control-allow-origin': '*', 'access-control-allow-headers': '*', 'access-control-allow-methods': 'POST, PATCH, OPTIONS, DELETE, GET, PUT', 'access-control-max-age': '600', 'strict-transport-security': 'max-age=31536000; includeSubDomains', 'x-trace-id': '41b17a4c2b8087421ea39da8396f3c3e', 'via': '1.1 google, 1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'}, status_code: 400, body: {'detail': {'status': 'voice_limit_reached', 'message': 'You have reached your maximum amount of custom voices (30 / 30). You can upgrade your subscription to increase your custom voice limit.'}}
2025-05-28 06:35:47,207 - api_logger - INFO - Audio generation completed. 0/2 files generated
2025-05-28 06:35:47,207 - api_logger - INFO - Updated job audio_job_ae5418429afa: status=partial, progress=0
2025-05-28 06:35:47,207 - api_logger - WARNING - Partial completion for job audio_job_ae5418429afa: 0/2 files
