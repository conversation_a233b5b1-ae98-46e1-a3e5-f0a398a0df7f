"""
Test script to download and play audio files from S3 URLs.
Demonstrates how to access audio files returned by the API.
"""

import requests
import os
from pathlib import Path

def download_audio_from_s3(s3_url, local_filename=None):
    """
    Download audio file from S3 URL.
    
    Args:
        s3_url: The S3 URL returned by the API
        local_filename: Optional local filename to save as
    
    Returns:
        Path to downloaded file or None if failed
    """
    try:
        print(f"📥 Downloading audio from: {s3_url}")
        
        # Make request to S3 URL
        response = requests.get(s3_url, stream=True)
        response.raise_for_status()
        
        # Generate filename if not provided
        if not local_filename:
            local_filename = s3_url.split('/')[-1]
        
        # Ensure downloads directory exists
        downloads_dir = Path("downloads")
        downloads_dir.mkdir(exist_ok=True)
        
        file_path = downloads_dir / local_filename
        
        # Download file
        with open(file_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        print(f"✅ Audio downloaded successfully: {file_path}")
        print(f"📁 File size: {file_path.stat().st_size} bytes")
        
        return file_path
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Error downloading audio: {e}")
        return None
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return None

def test_api_and_download():
    """
    Test the audio generation API and download the resulting files.
    """
    import json
    
    # Example: Test with your actual API
    api_base_url = "http://127.0.0.1:8000"  # Change to your API URL
    
    # Headers for API authentication
    headers = {
        "api_key": "your_api_key_here",
        "api_secret": "your_api_secret_here",
        "Content-Type": "application/json"
    }
    
    # 1. Generate audio for questions
    print("🎤 Generating audio for questions...")
    
    audio_request = {
        "questions": [
            "What is your greatest strength?",
            "Tell me about yourself."
        ],
        "voice_id": "21m00Tcm4TlvDq8ikWAM",  # Rachel voice
        "model": "eleven_flash_v2_5"
    }
    
    try:
        # Start audio generation
        response = requests.post(
            f"{api_base_url}/audio/generate-audio",
            headers=headers,
            json=audio_request
        )
        
        if response.status_code == 200:
            job_data = response.json()
            job_id = job_data["job_id"]
            print(f"✅ Audio generation started. Job ID: {job_id}")
            
            # 2. Check job status and get S3 URLs
            print("⏳ Waiting for audio generation to complete...")
            
            import time
            max_attempts = 30
            attempt = 0
            
            while attempt < max_attempts:
                status_response = requests.get(
                    f"{api_base_url}/audio/job-status/{job_id}"
                )
                
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    
                    if status_data["status"] == "completed":
                        print("✅ Audio generation completed!")
                        
                        # 3. Download each audio file
                        for audio_file in status_data["audio_files"]:
                            s3_url = audio_file["file_url"]
                            question_text = audio_file["question_text"]
                            
                            print(f"\n📝 Question: {question_text}")
                            print(f"🔗 S3 URL: {s3_url}")
                            
                            # Download the file
                            local_file = download_audio_from_s3(
                                s3_url, 
                                f"question_{audio_file['question_index']}.mp3"
                            )
                            
                            if local_file:
                                print(f"🎵 You can now play: {local_file}")
                        
                        break
                    
                    elif status_data["status"] == "failed":
                        print(f"❌ Audio generation failed: {status_data.get('error_message')}")
                        break
                    
                    else:
                        print(f"⏳ Status: {status_data['status']} ({status_data.get('progress', 0)}%)")
                        time.sleep(2)
                        attempt += 1
                
                else:
                    print(f"❌ Error checking status: {status_response.status_code}")
                    break
            
            if attempt >= max_attempts:
                print("⏰ Timeout waiting for audio generation")
        
        else:
            print(f"❌ Error starting audio generation: {response.status_code}")
            print(f"Response: {response.text}")
    
    except Exception as e:
        print(f"❌ Error: {e}")

def test_direct_s3_access():
    """
    Test direct access to S3 URLs (if you already have them).
    """
    # Example S3 URLs (replace with actual URLs from your API responses)
    example_s3_urls = [
        "https://my-interview-practice.s3.us-east-1.amazonaws.com/audio/audio_job_abc123/question_0_20240115_103000.mp3",
        # Add more URLs here
    ]
    
    print("🧪 Testing direct S3 access...")
    
    for i, s3_url in enumerate(example_s3_urls):
        print(f"\n📁 Testing URL {i+1}: {s3_url}")
        
        # Test if URL is accessible
        try:
            response = requests.head(s3_url)
            if response.status_code == 200:
                print("✅ URL is accessible")
                print(f"📏 Content-Length: {response.headers.get('Content-Length', 'Unknown')} bytes")
                print(f"📄 Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
                
                # Download the file
                download_audio_from_s3(s3_url, f"test_audio_{i+1}.mp3")
            else:
                print(f"❌ URL not accessible: {response.status_code}")
        
        except Exception as e:
            print(f"❌ Error accessing URL: {e}")

if __name__ == "__main__":
    print("🎵 S3 Audio Download Test")
    print("=" * 50)
    
    # Choose test method
    print("\nChoose test method:")
    print("1. Test full API workflow (generate + download)")
    print("2. Test direct S3 URL access")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "1":
        test_api_and_download()
    elif choice == "2":
        test_direct_s3_access()
    else:
        print("❌ Invalid choice")
