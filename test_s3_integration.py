"""
Test script to verify S3 integration for audio file uploads.
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api.utils.s3_service import s3_service
from api.utils.tts_service import tts_service

async def test_s3_integration():
    """Test the S3 integration with a simple audio generation."""
    
    print("🧪 Testing S3 Integration for Audio Files")
    print("=" * 50)
    
    # Test 1: Check S3 bucket access
    print("1. Testing S3 bucket access...")
    if s3_service.check_bucket_access():
        print("✅ S3 bucket is accessible")
    else:
        print("❌ S3 bucket access failed")
        return False
    
    # Test 2: Generate a simple audio file
    print("\n2. Testing audio generation and S3 upload...")
    try:
        test_text = "Hello, this is a test audio file for S3 integration."
        voice_id = "21m00Tcm4TlvDq8ikWAM"  # Rachel voice
        job_id = "test_job_123"
        question_index = 0
        
        success, s3_url, duration = await tts_service.generate_audio_file(
            text=test_text,
            voice_id=voice_id,
            job_id=job_id,
            question_index=question_index,
            model="eleven_flash_v2_5"
        )
        
        if success:
            print(f"✅ Audio generated and uploaded successfully!")
            print(f"📁 S3 URL: {s3_url}")
            print(f"⏱️ Duration: {duration:.2f} seconds")
            
            # Test 3: Clean up the test file
            print("\n3. Testing file cleanup...")
            cleanup_success = s3_service.delete_audio_files(job_id)
            if cleanup_success:
                print("✅ Test files cleaned up successfully")
            else:
                print("⚠️ Warning: Could not clean up test files")
            
            return True
        else:
            print(f"❌ Audio generation failed: {s3_url}")
            return False
            
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        return False

async def test_multiple_files():
    """Test generating multiple audio files."""
    
    print("\n🧪 Testing Multiple Audio Files Generation")
    print("=" * 50)
    
    try:
        test_questions = [
            "What is your greatest strength?",
            "Tell me about yourself.",
            "Why do you want to work here?"
        ]
        voice_id = "21m00Tcm4TlvDq8ikWAM"  # Rachel voice
        job_id = "test_multiple_123"
        
        audio_files = await tts_service.generate_multiple_audio_files(
            questions=test_questions,
            voice_id=voice_id,
            job_id=job_id,
            model="eleven_flash_v2_5"
        )
        
        print(f"✅ Generated {len(audio_files)} out of {len(test_questions)} audio files")
        
        for i, audio_file in enumerate(audio_files):
            print(f"  📁 File {i+1}: {audio_file.file_url}")
            print(f"     ⏱️ Duration: {audio_file.duration_seconds:.2f}s")
        
        # Clean up
        cleanup_success = s3_service.delete_audio_files(job_id)
        if cleanup_success:
            print("✅ Test files cleaned up successfully")
        
        return len(audio_files) == len(test_questions)
        
    except Exception as e:
        print(f"❌ Error during multiple files test: {str(e)}")
        return False

if __name__ == "__main__":
    async def main():
        print("🚀 Starting S3 Integration Tests")
        print("=" * 50)
        
        # Test single file
        single_test_passed = await test_s3_integration()
        
        # Test multiple files
        multiple_test_passed = await test_multiple_files()
        
        print("\n📊 Test Results Summary")
        print("=" * 50)
        print(f"Single file test: {'✅ PASSED' if single_test_passed else '❌ FAILED'}")
        print(f"Multiple files test: {'✅ PASSED' if multiple_test_passed else '❌ FAILED'}")
        
        if single_test_passed and multiple_test_passed:
            print("\n🎉 All tests passed! S3 integration is working correctly.")
            return True
        else:
            print("\n⚠️ Some tests failed. Please check the configuration.")
            return False
    
    # Run the tests
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
